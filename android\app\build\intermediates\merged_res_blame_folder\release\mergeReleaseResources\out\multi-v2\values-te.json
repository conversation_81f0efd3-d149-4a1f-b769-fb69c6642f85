{"logs": [{"outputFile": "com.example.wargani.app-mergeReleaseResources-45:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2845,2947,3055,3157,3258,3364,3471,6165", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2942,3050,3152,3253,3359,3466,3590,6261"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,277,356,502,671,758", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "173,272,351,497,666,753,837"}, "to": {"startLines": "38,41,55,56,59,60,61", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3850,4125,5859,5938,6266,6435,6522", "endColumns": "72,98,78,145,168,86,83", "endOffsets": "3918,4219,5933,6079,6430,6517,6601"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,2845", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,2921"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,445,535,640,759,837,913,1004,1097,1192,1286,1386,1479,1574,1669,1760,1851,1934,2048,2150,2247,2362,2465,2580,2742,6084", "endColumns": "116,111,110,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,82,113,101,96,114,102,114,161,102,80", "endOffsets": "217,329,440,530,635,754,832,908,999,1092,1187,1281,1381,1474,1569,1664,1755,1846,1929,2043,2145,2242,2357,2460,2575,2737,2840,6160"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3595", "endColumns": "137", "endOffsets": "3728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "39,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "3923,4224,4332,4443", "endColumns": "110,107,110,106", "endOffsets": "4029,4327,4438,4545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,263,382,514,640,790,911,1044,1144,1299,1439", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "167,258,377,509,635,785,906,1039,1139,1294,1434,1567"}, "to": {"startLines": "37,40,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3733,4034,4550,4669,4801,4927,5077,5198,5331,5431,5586,5726", "endColumns": "116,90,118,131,125,149,120,132,99,154,139,132", "endOffsets": "3845,4120,4664,4796,4922,5072,5193,5326,5426,5581,5721,5854"}}]}]}