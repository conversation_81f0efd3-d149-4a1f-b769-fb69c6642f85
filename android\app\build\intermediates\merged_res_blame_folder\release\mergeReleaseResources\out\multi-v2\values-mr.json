{"logs": [{"outputFile": "com.example.wargani.app-mergeReleaseResources-45:/values-mr/values-mr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,261,343,480,649,729", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "172,256,338,475,644,724,802"}, "to": {"startLines": "38,41,55,56,59,60,61", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3777,4037,5726,5808,6126,6295,6375", "endColumns": "71,83,81,136,168,79,77", "endOffsets": "3844,4116,5803,5940,6290,6370,6448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "36", "startColumns": "4", "startOffsets": "3527", "endColumns": "142", "endOffsets": "3665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "29,30,31,32,33,34,35,58", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2795,2895,2999,3100,3203,3305,3410,6025", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "2890,2994,3095,3198,3300,3405,3522,6121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2210,2319,2421,2535,2692,2795", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,95,108,101,113,156,102,79", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2205,2314,2416,2530,2687,2790,2870"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,322,429,519,621,733,811,888,979,1072,1165,1262,1362,1455,1550,1644,1735,1826,1906,2013,2114,2210,2319,2421,2535,2692,5945", "endColumns": "110,105,106,89,101,111,77,76,90,92,92,96,99,92,94,93,90,90,79,106,100,95,108,101,113,156,102,79", "endOffsets": "211,317,424,514,616,728,806,883,974,1067,1160,1257,1357,1450,1545,1639,1730,1821,1901,2008,2109,2205,2314,2416,2530,2687,2790,6020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,162,249,371,500,629,763,895,1029,1125,1269,1414", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "157,244,366,495,624,758,890,1024,1120,1264,1409,1535"}, "to": {"startLines": "37,40,45,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3670,3950,4435,4557,4686,4815,4949,5081,5215,5311,5455,5600", "endColumns": "106,86,121,128,128,133,131,133,95,143,144,125", "endOffsets": "3772,4032,4552,4681,4810,4944,5076,5210,5306,5450,5595,5721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "39,42,43,44", "startColumns": "4,4,4,4", "startOffsets": "3849,4121,4222,4333", "endColumns": "100,100,110,101", "endOffsets": "3945,4217,4328,4430"}}]}]}