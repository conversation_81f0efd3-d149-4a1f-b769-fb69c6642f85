<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res"><file name="launch_background" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\drawable\launch_background.xml" qualifiers="" type="drawable"/><file name="launch_background" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\drawable-v21\launch_background.xml" qualifiers="v21" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file><file path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\res\values-night\styles.xml" qualifiers="night-v8"><style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style><style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\release\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\build\generated\res\processReleaseGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\build\generated\res\resValues\release"/><source path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\build\generated\res\processReleaseGoogleServices"><file path="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\build\generated\res\processReleaseGoogleServices\values\values.xml" qualifiers=""><string name="gcm_defaultSenderId" translatable="false">813599718898</string><string name="google_api_key" translatable="false">AIzaSyAa26qGZst3pS8yINpUlKCbzqAaIfgRXN4</string><string name="google_app_id" translatable="false">1:813599718898:android:bcb271c49f000482777f6b</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyAa26qGZst3pS8yINpUlKCbzqAaIfgRXN4</string><string name="google_storage_bucket" translatable="false">wargani.firebasestorage.app</string><string name="project_id" translatable="false">wargani</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processReleaseGoogleServices" generated-set="res-processReleaseGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>