<lint-module
    format="1"
    dir="C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
