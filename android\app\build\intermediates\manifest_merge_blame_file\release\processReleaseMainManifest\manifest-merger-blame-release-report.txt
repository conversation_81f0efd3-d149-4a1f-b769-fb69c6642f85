1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.wargani"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!-- Storage permissions for different Android versions -->
11    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
11-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:3:5-80
11-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:3:22-77
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:4:5-81
12-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:4:22-78
13    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
13-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:5:5-82
13-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:5:22-79
14    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
14-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:6:5-76
14-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:6:22-73
15    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
15-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:7:5-75
15-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:7:22-72
16    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
16-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:8:5-75
16-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:8:22-72
17
18    <!-- Camera permission for receipt photos -->
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:11:5-65
19-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:11:22-62
20
21    <!-- Internet permission for Firebase -->
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:14:5-67
22-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:14:22-64
23    <!--
24         Required to query activities that can process text, see:
25         https://developer.android.com/training/package-visibility and
26         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
27
28         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
29    -->
30    <queries>
30-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:53:5-58:15
31        <intent>
31-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:54:9-57:18
32            <action android:name="android.intent.action.PROCESS_TEXT" />
32-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:55:13-72
32-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:55:21-70
33
34            <data android:mimeType="text/plain" />
34-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:56:13-50
34-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:56:19-48
35        </intent>
36    </queries>
37
38    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
38-->[:local_auth_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\local_auth_android-1.0.49\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-72
38-->[:local_auth_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\local_auth_android-1.0.49\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-69
39    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
39-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
39-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-5.0.2\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
40    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- suppress DeprecatedClassUsageInspection -->
40-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
40-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-65
41    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
41-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:5-74
41-->[androidx.biometric:biometric:1.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cceca150324ee75eadce8003b387b1fb\transformed\biometric-1.1.0\AndroidManifest.xml:27:22-71
42    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
42-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:5-79
42-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:25:22-76
43    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
43-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:5-88
43-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:26:22-85
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
44-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:5-82
44-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:27:22-79
45    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
45-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:5-110
45-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:26:22-107
46
47    <permission
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
48        android:name="com.example.wargani.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
49        android:protectionLevel="signature" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
50
51    <uses-permission android:name="com.example.wargani.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
52
53    <application
54        android:name="android.app.Application"
54-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:18:9-42
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
56        android:extractNativeLibs="true"
57        android:icon="@mipmap/ic_launcher"
57-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:19:9-43
58        android:label="wargani" >
58-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:17:9-32
59        <activity
59-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:20:9-41:20
60            android:name="com.example.wargani.MainActivity"
60-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:21:13-41
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:26:13-163
62            android:exported="true"
62-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:22:13-36
63            android:hardwareAccelerated="true"
63-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:27:13-47
64            android:launchMode="singleTop"
64-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:23:13-43
65            android:taskAffinity=""
65-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:24:13-36
66            android:theme="@style/LaunchTheme"
66-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:25:13-47
67            android:windowSoftInputMode="adjustResize" >
67-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:28:13-55
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
75-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:33:13-36:17
76                android:name="io.flutter.embedding.android.NormalTheme"
76-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:34:15-70
77                android:resource="@style/NormalTheme" />
77-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:35:15-52
78
79            <intent-filter>
79-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:37:13-40:29
80                <action android:name="android.intent.action.MAIN" />
80-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:38:17-68
80-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:38:25-66
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:39:17-76
82-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:39:27-74
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
89-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:44:9-46:33
90            android:name="flutterEmbedding"
90-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:45:13-44
91            android:value="2" />
91-->C:\Users\<USER>\AMSSoftX\project\Andriod Projects\Andriod Apps Workspace\updated\wargani\android\app\src\main\AndroidManifest.xml:46:13-30
92        <!--
93           Declares a provider which allows us to store files to share in
94           '.../caches/share_plus' and grant the receiving action access
95        -->
96        <provider
96-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:9-21:20
97            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
97-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-77
98            android:authorities="com.example.wargani.flutter.share_provider"
98-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-74
99            android:exported="false"
99-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-37
100            android:grantUriPermissions="true" >
100-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:17:13-47
101            <meta-data
101-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
102                android:name="android.support.FILE_PROVIDER_PATHS"
102-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
103                android:resource="@xml/flutter_share_file_paths" />
103-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
104        </provider>
105        <!--
106           This manifest declared broadcast receiver allows us to use an explicit
107           Intent when creating a PendingItent to be informed of the user's choice
108        -->
109        <receiver
109-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:9-32:20
110            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
110-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:13-82
111            android:exported="false" >
111-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-37
112            <intent-filter>
112-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:13-31:29
113                <action android:name="EXTRA_CHOSEN_COMPONENT" />
113-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-65
113-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:25-62
114            </intent-filter>
115        </receiver>
116
117        <provider
117-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
118            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
118-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
119            android:authorities="com.example.wargani.flutter.image_provider"
119-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
120            android:exported="false"
120-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
121            android:grantUriPermissions="true" >
121-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
122            <meta-data
122-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
123                android:name="android.support.FILE_PROVIDER_PATHS"
123-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
124                android:resource="@xml/flutter_image_picker_file_paths" />
124-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
125        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
126        <service
126-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
127            android:name="com.google.android.gms.metadata.ModuleDependencies"
127-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
128            android:enabled="false"
128-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
129            android:exported="false" >
129-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
130            <intent-filter>
130-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
131                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
131-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
131-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
132            </intent-filter>
133
134            <meta-data
134-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
135                android:name="photopicker_activity:0:required"
135-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
136                android:value="" />
136-->[:image_picker_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\image_picker_android-0.8.12+23\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
137        </service>
138
139        <activity
139-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
140            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
140-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
141            android:exported="false"
141-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
142            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
142-->[:url_launcher_android] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\url_launcher_android-6.3.16\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
143
144        <service
144-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-16:19
145            android:name="com.google.firebase.components.ComponentDiscoveryService"
145-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:18-89
146            android:directBootAware="true"
146-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
147            android:exported="false" >
147-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:14:13-37
148            <meta-data
148-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-15:85
149                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
149-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:17-128
150                android:value="com.google.firebase.components.ComponentRegistrar" />
150-->[:firebase_analytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_analytics-10.10.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-82
151            <meta-data
151-->[:firebase_crashlytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_crashlytics-3.5.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
152                android:name="com.google.firebase.components:io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar"
152-->[:firebase_crashlytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_crashlytics-3.5.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-130
153                android:value="com.google.firebase.components.ComponentRegistrar" />
153-->[:firebase_crashlytics] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_crashlytics-3.5.7\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
154            <meta-data
154-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-11:85
155                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
155-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:17-124
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[:firebase_core] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\firebase_core-2.32.0\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:17-82
157            <meta-data
157-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:15:13-17:85
158                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
158-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:16:17-126
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:17:17-82
160            <meta-data
160-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:18:13-20:85
161                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
161-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:19:17-115
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-crashlytics:18.6.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\3096e483490636d17269ad07947bfd5d\transformed\jetified-firebase-crashlytics-18.6.3\AndroidManifest.xml:20:17-82
163            <meta-data
163-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:37:13-39:85
164                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
164-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:38:17-139
165                android:value="com.google.firebase.components.ComponentRegistrar" />
165-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:39:17-82
166            <meta-data
166-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:29:13-31:85
167                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
167-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:30:17-117
168                android:value="com.google.firebase.components.ComponentRegistrar" />
168-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:31:17-82
169            <meta-data
169-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
170                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
170-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
171                android:value="com.google.firebase.components.ComponentRegistrar" />
171-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
172            <meta-data
172-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
173                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
173-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
174                android:value="com.google.firebase.components.ComponentRegistrar" />
174-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f5aeca28548f7e99154f01d4ebd38e3f\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
175            <meta-data
175-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
176                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
176-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
177                android:value="com.google.firebase.components.ComponentRegistrar" />
177-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\87b95cc5a110b26ff0418911538701c7\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
178            <meta-data
178-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
179                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
179-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
180                android:value="com.google.firebase.components.ComponentRegistrar" />
180-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
181            <meta-data
181-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\09447a00b81be1040b6b98273d870525\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:27:13-29:85
182                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
182-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\09447a00b81be1040b6b98273d870525\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:28:17-115
183                android:value="com.google.firebase.components.ComponentRegistrar" />
183-->[com.google.firebase:firebase-datatransport:18.1.8] C:\Users\<USER>\.gradle\caches\8.12\transforms\09447a00b81be1040b6b98273d870525\transformed\jetified-firebase-datatransport-18.1.8\AndroidManifest.xml:29:17-82
184        </service>
185
186        <provider
186-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.13.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-16:20
187            android:name="net.nfet.flutter.printing.PrintFileProvider"
187-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.13.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-71
188            android:authorities="com.example.wargani.flutter.printing"
188-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.13.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-68
189            android:exported="false"
189-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.13.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
190            android:grantUriPermissions="true" >
190-->[:printing] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\printing-5.13.1\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-47
191            <meta-data
191-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:13-20:68
192                android:name="android.support.FILE_PROVIDER_PATHS"
192-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:17-67
193                android:resource="@xml/flutter_printing_file_paths" />
193-->[:share_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\share_plus-8.0.3\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:17-65
194        </provider>
195
196        <property
196-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:30:9-32:61
197            android:name="android.adservices.AD_SERVICES_CONFIG"
197-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:31:13-65
198            android:resource="@xml/ga_ad_services_config" />
198-->[com.google.android.gms:play-services-measurement-api:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\7cdb0294d0a207b4579e9be08ababb57\transformed\jetified-play-services-measurement-api-21.6.1\AndroidManifest.xml:32:13-58
199
200        <service
200-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:22:9-25:40
201            android:name="com.google.firebase.sessions.SessionLifecycleService"
201-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:23:13-80
202            android:enabled="true"
202-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:24:13-35
203            android:exported="false" />
203-->[com.google.firebase:firebase-sessions:1.2.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\8bdc956f9779c94b875b9f056b9a8dbb\transformed\jetified-firebase-sessions-1.2.3\AndroidManifest.xml:25:13-37
204
205        <provider
205-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
206            android:name="com.google.firebase.provider.FirebaseInitProvider"
206-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
207            android:authorities="com.example.wargani.firebaseinitprovider"
207-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
208            android:directBootAware="true"
208-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
209            android:exported="false"
209-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
210            android:initOrder="100" />
210-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\47cad31ff74cf892ea2d0a21a66731dc\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
211
212        <receiver
212-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:29:9-33:20
213            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
213-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:30:13-85
214            android:enabled="true"
214-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:31:13-35
215            android:exported="false" >
215-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:32:13-37
216        </receiver>
217
218        <service
218-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:35:9-38:40
219            android:name="com.google.android.gms.measurement.AppMeasurementService"
219-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:36:13-84
220            android:enabled="true"
220-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:37:13-35
221            android:exported="false" />
221-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:38:13-37
222        <service
222-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:39:9-43:72
223            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
223-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:40:13-87
224            android:enabled="true"
224-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:41:13-35
225            android:exported="false"
225-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:42:13-37
226            android:permission="android.permission.BIND_JOB_SERVICE" />
226-->[com.google.android.gms:play-services-measurement:21.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\b14c3cf3266a93fddd87f3e674c28c67\transformed\jetified-play-services-measurement-21.6.1\AndroidManifest.xml:43:13-69
227
228        <provider
228-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
229            android:name="androidx.startup.InitializationProvider"
229-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
230            android:authorities="com.example.wargani.androidx-startup"
230-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
231            android:exported="false" >
231-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
232            <meta-data
232-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
233-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
234                android:value="androidx.startup" />
234-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
235            <meta-data
235-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
236                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
236-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
237                android:value="androidx.startup" />
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
238        </provider>
239
240        <uses-library
240-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
241            android:name="androidx.window.extensions"
241-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
242            android:required="false" />
242-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
243        <uses-library
243-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
244            android:name="androidx.window.sidecar"
244-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
245            android:required="false" />
245-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
246        <uses-library
246-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
247            android:name="android.ext.adservices"
247-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
248            android:required="false" />
248-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.12\transforms\ed9bdd709f3d824b8d47f92637346759\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
249
250        <meta-data
250-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
251            android:name="com.google.android.gms.version"
251-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
252            android:value="@integer/google_play_services_version" />
252-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
253
254        <receiver
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
255            android:name="androidx.profileinstaller.ProfileInstallReceiver"
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
256            android:directBootAware="false"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
257            android:enabled="true"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
258            android:exported="true"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
259            android:permission="android.permission.DUMP" >
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
261                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
262            </intent-filter>
263            <intent-filter>
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
264                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
265            </intent-filter>
266            <intent-filter>
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
267                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
268            </intent-filter>
269            <intent-filter>
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
270                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
271            </intent-filter>
272        </receiver>
273
274        <service
274-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
275            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
275-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
276            android:exported="false" >
276-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
277            <meta-data
277-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
278                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
278-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
279                android:value="cct" />
279-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\4dd55bdee0a4e18f01a67f197e6c9ccf\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
280        </service>
281        <service
281-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
282            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
282-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
283            android:exported="false"
283-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
284            android:permission="android.permission.BIND_JOB_SERVICE" >
284-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
285        </service>
286
287        <receiver
287-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
288            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
288-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
289            android:exported="false" />
289-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.12\transforms\a93e75e64be64d9ba356bb6c65cbb971\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
290    </application>
291
292</manifest>
