{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AMSSoftX\\project\\Andriod Projects\\Andriod Apps Workspace\\updated\\wargani\\android\\app\\.cxx\\RelWithDebInfo\\46j6m583\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AMSSoftX\\project\\Andriod Projects\\Andriod Apps Workspace\\updated\\wargani\\android\\app\\.cxx\\RelWithDebInfo\\46j6m583\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}