com.example.wargani:xml/image_share_filepaths = 0x7f120004
com.example.wargani:xml/flutter_share_file_paths = 0x7f120002
com.example.wargani:xml/flutter_printing_file_paths = 0x7f120001
com.example.wargani:styleable/ViewStubCompat = 0x7f110043
com.example.wargani:styleable/ViewBackgroundHelper = 0x7f110042
com.example.wargani:styleable/Toolbar = 0x7f110040
com.example.wargani:styleable/TextAppearance = 0x7f11003f
com.example.wargani:styleable/SwitchPreference = 0x7f11003d
com.example.wargani:styleable/SwitchCompat = 0x7f11003c
com.example.wargani:attr/alertDialogTheme = 0x7f040028
com.example.wargani:styleable/SplitPairRule = 0x7f110038
com.example.wargani:styleable/Spinner = 0x7f110036
com.example.wargani:styleable/SearchView = 0x7f110034
com.example.wargani:styleable/RecycleListView = 0x7f110032
com.example.wargani:styleable/PreferenceGroup = 0x7f11002f
com.example.wargani:styleable/PopupWindow = 0x7f11002a
com.example.wargani:style/AlertDialog.AppCompat = 0x7f100000
com.example.wargani:styleable/MultiSelectListPreference = 0x7f110029
com.example.wargani:id/info = 0x7f090079
com.example.wargani:styleable/LinearLayoutCompat_Layout = 0x7f110023
com.example.wargani:dimen/abc_text_size_caption_material = 0x7f07003f
com.example.wargani:style/Base.Widget.AppCompat.SeekBar = 0x7f10009a
com.example.wargani:styleable/LinearLayoutCompat = 0x7f110022
com.example.wargani:color/abc_secondary_text_material_light = 0x7f060012
com.example.wargani:styleable/GradientColor = 0x7f110020
com.example.wargani:styleable/Fragment = 0x7f11001e
com.example.wargani:id/multiply = 0x7f090085
com.example.wargani:styleable/EditTextPreference = 0x7f11001b
com.example.wargani:attr/subtitleTextAppearance = 0x7f040134
com.example.wargani:styleable/CoordinatorLayout = 0x7f110017
com.example.wargani:attr/actionModePopupWindowStyle = 0x7f040017
com.example.wargani:layout/abc_screen_toolbar = 0x7f0c0017
com.example.wargani:styleable/AppCompatTextHelper = 0x7f11000e
com.example.wargani:styleable/AnimatedStateListDrawableTransition = 0x7f11000b
com.example.wargani:styleable/AnimatedStateListDrawableItem = 0x7f11000a
com.example.wargani:dimen/abc_action_bar_default_padding_end_material = 0x7f070003
com.example.wargani:drawable/notification_oversize_large_icon_bg = 0x7f080070
com.example.wargani:styleable/AlertDialog = 0x7f110008
com.example.wargani:style/Widget.Support.CoordinatorLayout = 0x7f100181
com.example.wargani:style/Widget.AppCompat.TextView = 0x7f10017b
com.example.wargani:style/Widget.AppCompat.SearchView = 0x7f100173
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f100033
com.example.wargani:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f10007f
com.example.wargani:style/Widget.AppCompat.RatingBar = 0x7f100170
com.example.wargani:style/Widget.AppCompat.PopupWindow = 0x7f10016d
com.example.wargani:string/abc_searchview_description_clear = 0x7f0f0013
com.example.wargani:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f10016c
com.example.wargani:style/Widget.AppCompat.ListPopupWindow = 0x7f100167
com.example.wargani:attr/windowMinWidthMinor = 0x7f04017a
com.example.wargani:styleable/ActionMode = 0x7f110004
com.example.wargani:color/dim_foreground_material_dark = 0x7f060033
com.example.wargani:style/Widget.AppCompat.Light.SearchView = 0x7f100164
com.example.wargani:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f100163
com.example.wargani:style/Widget.AppCompat.Light.PopupMenu = 0x7f100162
com.example.wargani:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f10015f
com.example.wargani:layout/preference_information_material = 0x7f0c0032
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f100157
com.example.wargani:color/abc_btn_colored_borderless_text_material = 0x7f060002
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f100155
com.example.wargani:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f10015d
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f100154
com.example.wargani:id/recycler_view = 0x7f090096
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f100153
com.example.wargani:drawable/abc_ic_go_search_api_material = 0x7f080019
com.example.wargani:style/Widget.AppCompat.ImageButton = 0x7f10014f
com.example.wargani:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f100064
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f10002a
com.example.wargani:styleable/CoordinatorLayout_Layout = 0x7f110018
com.example.wargani:style/Widget.AppCompat.DrawerArrowToggle = 0x7f10014c
com.example.wargani:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f10014a
com.example.wargani:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f100148
com.example.wargani:attr/panelMenuListWidth = 0x7f0400ef
com.example.wargani:style/Widget.AppCompat.ButtonBar = 0x7f100147
com.example.wargani:id/center_vertical = 0x7f09004f
com.example.wargani:style/Widget.AppCompat.Button.Small = 0x7f100146
com.example.wargani:style/Widget.AppCompat.Button.Colored = 0x7f100145
com.example.wargani:styleable/PreferenceTheme = 0x7f110031
com.example.wargani:style/Widget.AppCompat.Button = 0x7f100141
com.example.wargani:styleable/FontFamilyFont = 0x7f11001d
com.example.wargani:style/Widget.AppCompat.ActionBar.TabView = 0x7f10013a
com.example.wargani:layout/notification_template_part_time = 0x7f0c002a
com.example.wargani:style/Widget.AppCompat.ActionBar.Solid = 0x7f100137
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f100152
com.example.wargani:style/ThemeOverlay.AppCompat.Light = 0x7f100135
com.example.wargani:attr/contentInsetStart = 0x7f040066
com.example.wargani:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f100134
com.example.wargani:color/abc_hint_foreground_material_light = 0x7f060008
com.example.wargani:style/ThemeOverlay.AppCompat.DayNight = 0x7f100131
com.example.wargani:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f100130
com.example.wargani:id/report_drawn = 0x7f090097
com.example.wargani:style/Theme.AppCompat.NoActionBar = 0x7f10012c
com.example.wargani:style/Widget.AppCompat.EditText = 0x7f10014e
com.example.wargani:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f10003a
com.example.wargani:id/uniform = 0x7f0900dd
com.example.wargani:style/Theme.AppCompat.Light.DarkActionBar = 0x7f100126
com.example.wargani:id/submenuarrow = 0x7f0900bb
com.example.wargani:style/Theme.AppCompat.Empty = 0x7f100124
com.example.wargani:layout/select_dialog_item_material = 0x7f0c003b
com.example.wargani:style/Theme.AppCompat.Dialog = 0x7f100120
com.example.wargani:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f10011e
com.example.wargani:style/Theme.AppCompat.CompactMenu = 0x7f100118
com.example.wargani:style/Theme.AppCompat = 0x7f100117
com.example.wargani:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f100114
com.example.wargani:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0900e1
com.example.wargani:style/TextAppearance.Compat.Notification.Time = 0x7f100112
com.example.wargani:styleable/MenuGroup = 0x7f110026
com.example.wargani:style/TextAppearance.Compat.Notification = 0x7f10010f
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f100151
com.example.wargani:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f10008c
com.example.wargani:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f10010e
com.example.wargani:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f10010c
com.example.wargani:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f10010b
com.example.wargani:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.example.wargani:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f1000dd
com.example.wargani:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f10010a
com.example.wargani:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f100108
com.example.wargani:style/Widget.AppCompat.Spinner.DropDown = 0x7f100178
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f100103
com.example.wargani:style/Widget.Compat.NotificationActionText = 0x7f100180
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f100102
com.example.wargani:color/highlighted_text_material_light = 0x7f06003b
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1000ff
com.example.wargani:color/bright_foreground_inverse_material_dark = 0x7f060025
com.example.wargani:id/image = 0x7f090078
com.example.wargani:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1000fa
com.example.wargani:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1000f8
com.example.wargani:id/alertTitle = 0x7f09003c
com.example.wargani:style/TextAppearance.AppCompat.Small = 0x7f1000f5
com.example.wargani:color/browser_actions_text_color = 0x7f06002b
com.example.wargani:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1000f1
com.example.wargani:styleable/BackgroundStyle = 0x7f110011
com.example.wargani:drawable/fingerprint_dialog_error = 0x7f08005e
com.example.wargani:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1000ef
com.example.wargani:attr/backgroundStacked = 0x7f04003b
com.example.wargani:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1000ee
com.example.wargani:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1000ec
com.example.wargani:id/browser_actions_menu_item_icon = 0x7f090048
com.example.wargani:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1000eb
com.example.wargani:drawable/abc_textfield_search_material = 0x7f080054
com.example.wargani:style/TextAppearance.AppCompat.Inverse = 0x7f1000e9
com.example.wargani:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f10007a
com.example.wargani:style/TextAppearance.AppCompat.Headline = 0x7f1000e8
com.example.wargani:id/action_menu_presenter = 0x7f090033
com.example.wargani:color/background_floating_material_light = 0x7f06001e
com.example.wargani:style/TextAppearance.AppCompat.Display4 = 0x7f1000e7
com.example.wargani:style/TextAppearance.AppCompat.Display3 = 0x7f1000e6
com.example.wargani:id/off = 0x7f09008c
com.example.wargani:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0b0004
com.example.wargani:style/TextAppearance.AppCompat.Display1 = 0x7f1000e4
com.example.wargani:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f1000de
com.example.wargani:attr/dropdownListPreferredItemHeight = 0x7f040087
com.example.wargani:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f1000dc
com.example.wargani:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f10009b
com.example.wargani:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f1000db
com.example.wargani:style/Widget.AppCompat.ActionBar.TabBar = 0x7f100138
com.example.wargani:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f1000da
com.example.wargani:style/TextAppearance.AppCompat.Display2 = 0x7f1000e5
com.example.wargani:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f1000d9
com.example.wargani:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f1000d7
com.example.wargani:dimen/disabled_alpha_material_dark = 0x7f070057
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f1000d6
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f1000d2
com.example.wargani:string/fingerprint_not_recognized = 0x7f0f0032
com.example.wargani:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f1000d0
com.example.wargani:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f1000ce
com.example.wargani:style/PreferenceSummaryTextStyle = 0x7f1000ca
com.example.wargani:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f10012a
com.example.wargani:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f10008e
com.example.wargani:style/PreferenceFragment.Material = 0x7f1000c7
com.example.wargani:style/PreferenceFragment = 0x7f1000c6
com.example.wargani:style/Preference.SwitchPreferenceCompat = 0x7f1000c3
com.example.wargani:attr/firstBaselineToTopHeight = 0x7f04009b
com.example.wargani:style/Preference.SwitchPreference = 0x7f1000c1
com.example.wargani:style/TextAppearance.AppCompat.Menu = 0x7f1000f2
com.example.wargani:style/Preference.Material = 0x7f1000bc
com.example.wargani:style/Preference.Information.Material = 0x7f1000bb
com.example.wargani:style/Preference.DropDown = 0x7f1000b8
com.example.wargani:style/Preference.DialogPreference.Material = 0x7f1000b7
com.example.wargani:styleable/ActionBarLayout = 0x7f110001
com.example.wargani:layout/notification_action = 0x7f0c0025
com.example.wargani:style/Preference.DialogPreference.EditTextPreference.Material = 0x7f1000b6
com.example.wargani:attr/buttonBarPositiveButtonStyle = 0x7f040043
com.example.wargani:style/Preference.DialogPreference = 0x7f1000b4
com.example.wargani:dimen/fingerprint_icon_size = 0x7f07005c
com.example.wargani:style/Preference.CheckBoxPreference = 0x7f1000b2
com.example.wargani:style/Preference.Category.Material = 0x7f1000b1
com.example.wargani:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f100107
com.example.wargani:style/Preference.Category = 0x7f1000b0
com.example.wargani:style/Preference = 0x7f1000af
com.example.wargani:anim/abc_fade_out = 0x7f010001
com.example.wargani:id/accessibility_custom_action_17 = 0x7f090010
com.example.wargani:style/Platform.Widget.AppCompat.Spinner = 0x7f1000ae
com.example.wargani:style/Platform.V21.AppCompat.Light = 0x7f1000ab
com.example.wargani:style/Platform.V21.AppCompat = 0x7f1000aa
com.example.wargani:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f10016f
com.example.wargani:attr/listPreferredItemHeightSmall = 0x7f0400d2
com.example.wargani:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f1000a9
com.example.wargani:id/ifRoom = 0x7f090077
com.example.wargani:dimen/abc_select_dialog_padding_start_material = 0x7f07003a
com.example.wargani:style/Platform.AppCompat.Light = 0x7f1000a6
com.example.wargani:color/switch_thumb_material_dark = 0x7f06005b
com.example.wargani:style/Animation.AppCompat.Dialog = 0x7f100003
com.example.wargani:style/Platform.AppCompat = 0x7f1000a5
com.example.wargani:id/preferences_sliding_pane_layout = 0x7f090092
com.example.wargani:style/NormalTheme = 0x7f1000a4
com.example.wargani:style/BasePreferenceThemeOverlay = 0x7f1000a2
com.example.wargani:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f1000a1
com.example.wargani:style/Widget.AppCompat.ActionButton = 0x7f10013b
com.example.wargani:attr/fontFamily = 0x7f04009d
com.example.wargani:style/Preference.PreferenceScreen = 0x7f1000bd
com.example.wargani:color/background_material_dark = 0x7f06001f
com.example.wargani:style/Base.Widget.AppCompat.Toolbar = 0x7f1000a0
com.example.wargani:style/Base.Widget.AppCompat.TextView = 0x7f10009e
com.example.wargani:style/TextAppearance.Compat.Notification.Info = 0x7f100110
com.example.wargani:attr/actionBarPopupTheme = 0x7f040002
com.example.wargani:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f100099
com.example.wargani:dimen/preferences_detail_width = 0x7f07007d
com.example.wargani:layout/browser_actions_context_menu_row = 0x7f0c001d
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f100039
com.example.wargani:style/Base.Widget.AppCompat.SearchView = 0x7f100098
com.example.wargani:styleable/ActionBar = 0x7f110000
com.example.wargani:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f100097
com.example.wargani:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.example.wargani:color/foreground_material_light = 0x7f060038
com.example.wargani:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f100094
com.example.wargani:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f10009d
com.example.wargani:style/Base.Widget.AppCompat.PopupWindow = 0x7f100092
com.example.wargani:style/Base.Widget.AppCompat.ListMenuView = 0x7f10008b
com.example.wargani:style/ThemeOverlay.AppCompat.ActionBar = 0x7f10012e
com.example.wargani:drawable/notification_template_icon_bg = 0x7f080071
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f100088
com.example.wargani:attr/ttcIndex = 0x7f04016c
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f100085
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f100084
com.example.wargani:style/Base.Widget.AppCompat.ImageButton = 0x7f100082
com.example.wargani:style/Base.Widget.AppCompat.EditText = 0x7f100081
com.example.wargani:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f10007e
com.example.wargani:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f10007d
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f100156
com.example.wargani:style/Base.Widget.AppCompat.ButtonBar = 0x7f100079
com.example.wargani:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f100076
com.example.wargani:style/Base.Widget.AppCompat.Button.Borderless = 0x7f100074
com.example.wargani:string/google_app_id = 0x7f0f0038
com.example.wargani:style/ThemeOverlay.AppCompat.Dialog = 0x7f100133
com.example.wargani:layout/preference_list_fragment = 0x7f0c0033
com.example.wargani:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f100027
com.example.wargani:style/Base.Widget.AppCompat.ActionMode = 0x7f100070
com.example.wargani:style/Base.Widget.AppCompat.ActionButton = 0x7f10006d
com.example.wargani:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f100069
com.example.wargani:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f100065
com.example.wargani:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f100063
com.example.wargani:style/Base.V7.Theme.AppCompat = 0x7f100060
com.example.wargani:style/Base.V28.Theme.AppCompat.Light = 0x7f10005f
com.example.wargani:id/action_bar_subtitle = 0x7f09002c
com.example.wargani:styleable/ButtonBarLayout = 0x7f110012
com.example.wargani:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f08003b
com.example.wargani:style/Base.V28.Theme.AppCompat = 0x7f10005e
com.example.wargani:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f10005d
com.example.wargani:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f100056
com.example.wargani:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f100055
com.example.wargani:color/switch_thumb_normal_material_light = 0x7f06005e
com.example.wargani:style/Base.V21.Theme.AppCompat.Dialog = 0x7f100053
com.example.wargani:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f10004f
com.example.wargani:styleable/ListPreference = 0x7f110025
com.example.wargani:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f100091
com.example.wargani:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f10004e
com.example.wargani:style/Base.ThemeOverlay.AppCompat = 0x7f10004b
com.example.wargani:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f100048
com.example.wargani:styleable/StateListDrawableItem = 0x7f11003b
com.example.wargani:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f100047
com.example.wargani:style/Widget.AppCompat.ActivityChooserView = 0x7f10013f
com.example.wargani:style/Preference.PreferenceScreen.Material = 0x7f1000be
com.example.wargani:drawable/notification_icon_background = 0x7f08006f
com.example.wargani:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f10017c
com.example.wargani:style/Base.Theme.AppCompat.Light.Dialog = 0x7f100046
com.example.wargani:style/TextAppearance.AppCompat.Tooltip = 0x7f1000fb
com.example.wargani:style/Base.Theme.AppCompat.Light = 0x7f100044
com.example.wargani:dimen/abc_action_bar_elevation_material = 0x7f070005
com.example.wargani:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f100042
com.example.wargani:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f100041
com.example.wargani:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f100040
com.example.wargani:styleable/AppCompatImageView = 0x7f11000c
com.example.wargani:style/Base.V7.Theme.AppCompat.Dialog = 0x7f100061
com.example.wargani:attr/tooltipFrameBackground = 0x7f040167
com.example.wargani:style/Base.Theme.AppCompat.CompactMenu = 0x7f10003e
com.example.wargani:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f10003b
com.example.wargani:style/Base.V23.Theme.AppCompat = 0x7f100059
com.example.wargani:dimen/notification_action_text_size = 0x7f07006a
com.example.wargani:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f10006a
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f100037
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f100034
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f100031
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f100030
com.example.wargani:id/accessibility_custom_action_20 = 0x7f090014
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f10002f
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f10002c
com.example.wargani:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f100025
com.example.wargani:dimen/tooltip_y_offset_non_touch = 0x7f070085
com.example.wargani:style/Theme.AppCompat.Light = 0x7f100125
com.example.wargani:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1000f4
com.example.wargani:attr/layout_anchor = 0x7f0400c1
com.example.wargani:array/delay_showing_prompt_models = 0x7f030003
com.example.wargani:style/Base.TextAppearance.AppCompat.Subhead = 0x7f100024
com.example.wargani:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f100023
com.example.wargani:attr/controlBackground = 0x7f040068
com.example.wargani:style/Base.TextAppearance.AppCompat.Menu = 0x7f10001e
com.example.wargani:style/Base.TextAppearance.AppCompat.Large = 0x7f100018
com.example.wargani:style/Base.V7.Theme.AppCompat.Light = 0x7f100062
com.example.wargani:style/Base.TextAppearance.AppCompat.Inverse = 0x7f100017
com.example.wargani:style/Base.TextAppearance.AppCompat.Display2 = 0x7f100013
com.example.wargani:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f100132
com.example.wargani:string/default_error_msg = 0x7f0f0027
com.example.wargani:style/Base.TextAppearance.AppCompat.Button = 0x7f100010
com.example.wargani:style/Base.TextAppearance.AppCompat.Caption = 0x7f100011
com.example.wargani:style/Base.TextAppearance.AppCompat.Body1 = 0x7f10000e
com.example.wargani:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f10007c
com.example.wargani:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f10000c
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f10002b
com.example.wargani:style/Base.DialogWindowTitle.AppCompat = 0x7f10000b
com.example.wargani:attr/contentInsetLeft = 0x7f040064
com.example.wargani:style/Base.Animation.AppCompat.Tooltip = 0x7f10000a
com.example.wargani:style/Base.Animation.AppCompat.DropDownUp = 0x7f100009
com.example.wargani:style/Base.Animation.AppCompat.Dialog = 0x7f100008
com.example.wargani:style/Base.Theme.AppCompat = 0x7f10003d
com.example.wargani:style/Base.AlertDialog.AppCompat = 0x7f100006
com.example.wargani:attr/listPreferredItemHeight = 0x7f0400d0
com.example.wargani:styleable/PreferenceFragmentCompat = 0x7f11002e
com.example.wargani:style/Animation.AppCompat.Tooltip = 0x7f100005
com.example.wargani:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f100143
com.example.wargani:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f10009f
com.example.wargani:style/AlertDialogCustom = 0x7f100002
com.example.wargani:string/v7_preference_on = 0x7f0f0042
com.example.wargani:attr/finishPrimaryWithSecondary = 0x7f040099
com.example.wargani:string/v7_preference_off = 0x7f0f0041
com.example.wargani:string/search_menu_title = 0x7f0f003e
com.example.wargani:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f10003c
com.example.wargani:string/project_id = 0x7f0f003d
com.example.wargani:dimen/abc_dialog_fixed_width_minor = 0x7f07001f
com.example.wargani:string/preference_copied = 0x7f0f003c
com.example.wargani:id/blocking = 0x7f090044
com.example.wargani:drawable/abc_ic_star_black_36dp = 0x7f080022
com.example.wargani:string/not_set = 0x7f0f003b
com.example.wargani:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f10011a
com.example.wargani:id/edit_text_id = 0x7f09005f
com.example.wargani:string/google_storage_bucket = 0x7f0f003a
com.example.wargani:xml/flutter_image_picker_file_paths = 0x7f120000
com.example.wargani:style/Widget.AppCompat.ActionMode = 0x7f10013e
com.example.wargani:id/time = 0x7f0900d0
com.example.wargani:string/google_api_key = 0x7f0f0037
com.example.wargani:id/alwaysDisallow = 0x7f090040
com.example.wargani:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f10004d
com.example.wargani:color/material_grey_50 = 0x7f060043
com.example.wargani:string/generic_error_user_canceled = 0x7f0f0036
com.example.wargani:attr/actionModeCloseButtonStyle = 0x7f040011
com.example.wargani:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f08003c
com.example.wargani:string/generic_error_no_device_credential = 0x7f0f0034
com.example.wargani:string/gcm_defaultSenderId = 0x7f0f0033
com.example.wargani:dimen/hint_pressed_alpha_material_light = 0x7f070063
com.example.wargani:attr/summaryOn = 0x7f04013a
com.example.wargani:style/Base.V7.Widget.AppCompat.EditText = 0x7f100066
com.example.wargani:string/fingerprint_error_user_canceled = 0x7f0f0031
com.example.wargani:string/fingerprint_error_no_fingerprints = 0x7f0f0030
com.example.wargani:string/fingerprint_error_hw_not_present = 0x7f0f002e
com.example.wargani:string/fingerprint_error_hw_not_available = 0x7f0f002d
com.example.wargani:string/expand_button_title = 0x7f0f0028
com.example.wargani:style/Base.V21.Theme.AppCompat.Light = 0x7f100054
com.example.wargani:string/call_notification_screening_text = 0x7f0f0022
com.example.wargani:attr/arrowShaftLength = 0x7f040032
com.example.wargani:string/call_notification_incoming_text = 0x7f0f0020
com.example.wargani:string/call_notification_hang_up_action = 0x7f0f001f
com.example.wargani:string/call_notification_answer_video_action = 0x7f0f001d
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f100038
com.example.wargani:string/common_google_play_services_unknown_issue = 0x7f0f0023
com.example.wargani:attr/actionButtonStyle = 0x7f04000b
com.example.wargani:id/androidx_window_activity_scope = 0x7f090041
com.example.wargani:id/fingerprint_error = 0x7f090067
com.example.wargani:string/abc_shareactionprovider_share_with_application = 0x7f0f0019
com.example.wargani:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1000ed
com.example.wargani:styleable/DialogPreference = 0x7f110019
com.example.wargani:string/abc_shareactionprovider_share_with = 0x7f0f0018
com.example.wargani:attr/negativeButtonText = 0x7f0400e3
com.example.wargani:string/abc_searchview_description_search = 0x7f0f0015
com.example.wargani:string/abc_prepend_shortcut_label = 0x7f0f0011
com.example.wargani:string/abc_menu_space_shortcut_label = 0x7f0f000f
com.example.wargani:string/abc_menu_meta_shortcut_label = 0x7f0f000d
com.example.wargani:style/Base.Widget.AppCompat.RatingBar = 0x7f100095
com.example.wargani:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f10006e
com.example.wargani:string/abc_menu_function_shortcut_label = 0x7f0f000c
com.example.wargani:string/abc_menu_enter_shortcut_label = 0x7f0f000b
com.example.wargani:string/abc_menu_delete_shortcut_label = 0x7f0f000a
com.example.wargani:string/abc_menu_alt_shortcut_label = 0x7f0f0008
com.example.wargani:string/abc_action_mode_done = 0x7f0f0003
com.example.wargani:drawable/btn_checkbox_unchecked_mtrl = 0x7f080058
com.example.wargani:id/ltr = 0x7f090082
com.example.wargani:string/abc_action_menu_overflow_description = 0x7f0f0002
com.example.wargani:dimen/abc_disabled_alpha_material_light = 0x7f070028
com.example.wargani:string/abc_action_bar_up_description = 0x7f0f0001
com.example.wargani:raw/firebase_common_keep = 0x7f0e0000
com.example.wargani:attr/autoSizeTextType = 0x7f040038
com.example.wargani:mipmap/ic_launcher = 0x7f0d0000
com.example.wargani:layout/support_simple_spinner_dropdown_item = 0x7f0c003e
com.example.wargani:layout/select_dialog_multichoice_material = 0x7f0c003c
com.example.wargani:string/abc_menu_shift_shortcut_label = 0x7f0f000e
com.example.wargani:layout/preference_widget_checkbox = 0x7f0c0036
com.example.wargani:layout/preference_information = 0x7f0c0031
com.example.wargani:id/wrap_content = 0x7f0900e6
com.example.wargani:styleable/MenuItem = 0x7f110027
com.example.wargani:id/action_mode_bar = 0x7f090034
com.example.wargani:layout/preference_dropdown_material = 0x7f0c0030
com.example.wargani:string/abc_action_bar_home_description = 0x7f0f0000
com.example.wargani:layout/preference_dialog_edittext = 0x7f0c002e
com.example.wargani:style/Theme.AppCompat.Light.NoActionBar = 0x7f10012b
com.example.wargani:layout/preference_category_material = 0x7f0c002d
com.example.wargani:layout/preference_category = 0x7f0c002c
com.example.wargani:layout/preference = 0x7f0c002b
com.example.wargani:layout/notification_template_part_chronometer = 0x7f0c0029
com.example.wargani:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.example.wargani:layout/notification_action_tombstone = 0x7f0c0026
com.example.wargani:dimen/abc_text_size_small_material = 0x7f070049
com.example.wargani:layout/image_frame = 0x7f0c0022
com.example.wargani:layout/go_to_setting = 0x7f0c0021
com.example.wargani:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f100096
com.example.wargani:layout/expand_button = 0x7f0c001f
com.example.wargani:layout/browser_actions_context_menu_page = 0x7f0c001c
com.example.wargani:attr/fontProviderPackage = 0x7f0400a2
com.example.wargani:layout/abc_tooltip = 0x7f0c001b
com.example.wargani:layout/abc_search_view = 0x7f0c0019
com.example.wargani:layout/abc_search_dropdown_item_icons_2line = 0x7f0c0018
com.example.wargani:layout/abc_screen_simple = 0x7f0c0015
com.example.wargani:style/TextAppearance.AppCompat.Title = 0x7f1000f9
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f1000d4
com.example.wargani:layout/abc_screen_content_include = 0x7f0c0014
com.example.wargani:layout/abc_popup_menu_item_layout = 0x7f0c0013
com.example.wargani:layout/abc_list_menu_item_icon = 0x7f0c000f
com.example.wargani:layout/abc_list_menu_item_checkbox = 0x7f0c000e
com.example.wargani:layout/abc_alert_dialog_title_material = 0x7f0c000a
com.example.wargani:attr/voiceIcon = 0x7f040170
com.example.wargani:id/search_bar = 0x7f0900a3
com.example.wargani:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f100049
com.example.wargani:layout/abc_action_menu_layout = 0x7f0c0003
com.example.wargani:layout/abc_popup_menu_header_item_layout = 0x7f0c0012
com.example.wargani:attr/progressBarStyle = 0x7f040103
com.example.wargani:layout/abc_action_bar_up_container = 0x7f0c0001
com.example.wargani:attr/shortcutMatchRequired = 0x7f040117
com.example.wargani:styleable/ActivityChooserView = 0x7f110005
com.example.wargani:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0b0003
com.example.wargani:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0b0002
com.example.wargani:attr/iconifiedByDefault = 0x7f0400b3
com.example.wargani:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0b0000
com.example.wargani:style/Base.Widget.AppCompat.Spinner = 0x7f10009c
com.example.wargani:string/abc_capital_off = 0x7f0f0006
com.example.wargani:integer/preferences_header_pane_weight = 0x7f0a0006
com.example.wargani:style/Theme.AppCompat.Light.Dialog = 0x7f100127
com.example.wargani:integer/preferences_detail_pane_weight = 0x7f0a0005
com.example.wargani:integer/google_play_services_version = 0x7f0a0004
com.example.wargani:integer/abc_config_activityDefaultDur = 0x7f0a0000
com.example.wargani:id/withText = 0x7f0900e5
com.example.wargani:id/visible_removing_fragment_view_tag = 0x7f0900e4
com.example.wargani:styleable/StateListDrawable = 0x7f11003a
com.example.wargani:layout/abc_list_menu_item_radio = 0x7f0c0011
com.example.wargani:integer/status_bar_notification_info_maxnum = 0x7f0a0007
com.example.wargani:id/unchecked = 0x7f0900dc
com.example.wargani:id/transition_position = 0x7f0900d9
com.example.wargani:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f100116
com.example.wargani:id/topToBottom = 0x7f0900d6
com.example.wargani:layout/abc_select_dialog_material = 0x7f0c001a
com.example.wargani:id/top = 0x7f0900d4
com.example.wargani:dimen/highlight_alpha_material_light = 0x7f07005f
com.example.wargani:id/text2 = 0x7f0900cd
com.example.wargani:id/title_template = 0x7f0900d3
com.example.wargani:id/tag_unhandled_key_event_manager = 0x7f0900c9
com.example.wargani:attr/actionBarWidgetTheme = 0x7f04000a
com.example.wargani:id/tag_transition_group = 0x7f0900c8
com.example.wargani:id/useLogo = 0x7f0900df
com.example.wargani:id/tag_screen_reader_focusable = 0x7f0900c6
com.example.wargani:style/Widget.AppCompat.SeekBar.Discrete = 0x7f100176
com.example.wargani:id/tag_on_receive_content_listener = 0x7f0900c4
com.example.wargani:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f10001a
com.example.wargani:id/tag_accessibility_pane_title = 0x7f0900c2
com.example.wargani:id/tag_accessibility_clickable_spans = 0x7f0900c0
com.example.wargani:id/switchWidget = 0x7f0900bd
com.example.wargani:id/submit_area = 0x7f0900bc
com.example.wargani:style/TextAppearance.Compat.Notification.Title = 0x7f100113
com.example.wargani:id/src_over = 0x7f0900b9
com.example.wargani:attr/paddingEnd = 0x7f0400ea
com.example.wargani:layout/abc_activity_chooser_view_list_item = 0x7f0c0007
com.example.wargani:id/src_in = 0x7f0900b8
com.example.wargani:layout/abc_alert_dialog_button_bar_material = 0x7f0c0008
com.example.wargani:style/PreferenceFragmentList.Material = 0x7f1000c9
com.example.wargani:attr/thumbTextPadding = 0x7f040152
com.example.wargani:id/src_atop = 0x7f0900b7
com.example.wargani:attr/backgroundTintMode = 0x7f04003d
com.example.wargani:id/split_action_bar = 0x7f0900b6
com.example.wargani:id/spinner = 0x7f0900b5
com.example.wargani:id/spacer = 0x7f0900b3
com.example.wargani:id/showTitle = 0x7f0900b2
com.example.wargani:id/showHome = 0x7f0900b1
com.example.wargani:id/select_dialog_listview = 0x7f0900ae
com.example.wargani:dimen/abc_dialog_min_width_minor = 0x7f070023
com.example.wargani:id/search_src_text = 0x7f0900aa
com.example.wargani:id/search_mag_icon = 0x7f0900a8
com.example.wargani:id/search_close_btn = 0x7f0900a5
com.example.wargani:attr/windowActionBarOverlay = 0x7f040173
com.example.wargani:string/abc_menu_sym_shortcut_label = 0x7f0f0010
com.example.wargani:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.example.wargani:id/search_badge = 0x7f0900a2
com.example.wargani:string/copy_toast_msg = 0x7f0f0026
com.example.wargani:id/scrollView = 0x7f0900a1
com.example.wargani:styleable/SwitchPreferenceCompat = 0x7f11003e
com.example.wargani:styleable/ListPopupWindow = 0x7f110024
com.example.wargani:id/scrollIndicatorUp = 0x7f0900a0
com.example.wargani:dimen/compat_button_padding_horizontal_material = 0x7f070052
com.example.wargani:styleable/MenuView = 0x7f110028
com.example.wargani:dimen/hint_pressed_alpha_material_dark = 0x7f070062
com.example.wargani:drawable/ic_call_answer_low = 0x7f080062
com.example.wargani:id/scrollIndicatorDown = 0x7f09009f
com.example.wargani:styleable/PreferenceFragment = 0x7f11002d
com.example.wargani:color/biometric_error_color = 0x7f060021
com.example.wargani:id/screen = 0x7f09009e
com.example.wargani:style/TextAppearance.AppCompat.Body1 = 0x7f1000e0
com.example.wargani:integer/abc_config_activityShortDur = 0x7f0a0001
com.example.wargani:id/save_overlay_view = 0x7f09009d
com.example.wargani:id/transition_scene_layoutid_cache = 0x7f0900da
com.example.wargani:id/rtl = 0x7f09009b
com.example.wargani:attr/titleTextAppearance = 0x7f040161
com.example.wargani:id/right_side = 0x7f09009a
com.example.wargani:style/Widget.AppCompat.CompoundButton.Switch = 0x7f10014b
com.example.wargani:id/right = 0x7f090098
com.example.wargani:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f100179
com.example.wargani:style/Base.Theme.AppCompat.Dialog = 0x7f10003f
com.example.wargani:style/Animation.AppCompat.DropDownUp = 0x7f100004
com.example.wargani:id/preferences_header = 0x7f090091
com.example.wargani:integer/cancel_button_image_alpha = 0x7f0a0002
com.example.wargani:drawable/abc_textfield_default_mtrl_alpha = 0x7f080051
com.example.wargani:id/view_tree_saved_state_registry_owner = 0x7f0900e2
com.example.wargani:id/parent_matrix = 0x7f09008f
com.example.wargani:attr/alertDialogButtonGroupStyle = 0x7f040025
com.example.wargani:id/parentPanel = 0x7f09008e
com.example.wargani:id/on = 0x7f09008d
com.example.wargani:id/notification_main_column_container = 0x7f09008b
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f100036
com.example.wargani:id/notification_background = 0x7f090089
com.example.wargani:id/none = 0x7f090087
com.example.wargani:id/never = 0x7f090086
com.example.wargani:style/Base.Widget.AppCompat.ListView.Menu = 0x7f10008f
com.example.wargani:attr/updatesContinuously = 0x7f04016d
com.example.wargani:id/message = 0x7f090083
com.example.wargani:dimen/compat_button_inset_vertical_material = 0x7f070051
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f100032
com.example.wargani:id/list_item = 0x7f090080
com.example.wargani:id/listMode = 0x7f09007f
com.example.wargani:attr/actionModeCutDrawable = 0x7f040014
com.example.wargani:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f100028
com.example.wargani:layout/abc_action_menu_item_layout = 0x7f0c0002
com.example.wargani:id/line3 = 0x7f09007e
com.example.wargani:drawable/notification_bg_low = 0x7f08006a
com.example.wargani:id/line1 = 0x7f09007d
com.example.wargani:id/left = 0x7f09007c
com.example.wargani:id/title = 0x7f0900d1
com.example.wargani:style/Theme.AppCompat.DialogWhenLarge = 0x7f100123
com.example.wargani:layout/abc_screen_simple_overlay_action_mode = 0x7f0c0016
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f10002d
com.example.wargani:id/icon_group = 0x7f090076
com.example.wargani:color/foreground_material_dark = 0x7f060037
com.example.wargani:id/icon_frame = 0x7f090075
com.example.wargani:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f100160
com.example.wargani:id/homeAsUp = 0x7f090073
com.example.wargani:style/Preference.SeekBarPreference.Material = 0x7f1000c0
com.example.wargani:id/hide_ime_id = 0x7f090071
com.example.wargani:id/home = 0x7f090072
com.example.wargani:id/ghost_view_holder = 0x7f09006e
com.example.wargani:id/middle = 0x7f090084
com.example.wargani:id/ghost_view = 0x7f09006d
com.example.wargani:id/fragment_container_view_tag = 0x7f09006c
com.example.wargani:style/TextAppearance.AppCompat.Medium = 0x7f1000f0
com.example.wargani:id/forever = 0x7f09006b
com.example.wargani:id/fingerprint_subtitle = 0x7f09006a
com.example.wargani:id/fingerprint_required = 0x7f090069
com.example.wargani:id/fingerprint_icon = 0x7f090068
com.example.wargani:id/fingerprint_description = 0x7f090066
com.example.wargani:drawable/abc_list_selector_disabled_holo_light = 0x7f080033
com.example.wargani:style/Widget.AppCompat.AutoCompleteTextView = 0x7f100140
com.example.wargani:id/group_divider = 0x7f090070
com.example.wargani:id/fill_vertical = 0x7f090065
com.example.wargani:attr/actionModeStyle = 0x7f04001b
com.example.wargani:id/accessibility_action_clickable_span = 0x7f090006
com.example.wargani:id/fill_horizontal = 0x7f090064
com.example.wargani:attr/dialogPreferenceStyle = 0x7f040072
com.example.wargani:layout/preference_widget_seekbar_material = 0x7f0c0038
com.example.wargani:id/expanded_menu = 0x7f090062
com.example.wargani:id/decor_content_parent = 0x7f09005a
com.example.wargani:id/custom = 0x7f090058
com.example.wargani:id/content = 0x7f090056
com.example.wargani:string/abc_searchview_description_voice = 0x7f0f0017
com.example.wargani:id/collapseActionView = 0x7f090055
com.example.wargani:id/clip_horizontal = 0x7f090053
com.example.wargani:id/checked = 0x7f090051
com.example.wargani:id/up = 0x7f0900de
com.example.wargani:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f100067
com.example.wargani:id/center_horizontal = 0x7f09004e
com.example.wargani:id/italic = 0x7f09007a
com.example.wargani:id/center = 0x7f09004d
com.example.wargani:style/Preference.SwitchPreference.Material = 0x7f1000c2
com.example.wargani:style/Base.V26.Theme.AppCompat.Light = 0x7f10005c
com.example.wargani:id/buttonPanel = 0x7f09004c
com.example.wargani:id/browser_actions_menu_view = 0x7f09004b
com.example.wargani:id/tag_window_insets_animation_callback = 0x7f0900cb
com.example.wargani:id/browser_actions_menu_item_text = 0x7f090049
com.example.wargani:style/LaunchTheme = 0x7f1000a3
com.example.wargani:id/browser_actions_header_text = 0x7f090047
com.example.wargani:id/alwaysAllow = 0x7f09003f
com.example.wargani:string/androidx_startup = 0x7f0f001b
com.example.wargani:id/go_to_setting_description = 0x7f09006f
com.example.wargani:id/adjacent = 0x7f09003b
com.example.wargani:id/add = 0x7f09003a
com.example.wargani:dimen/browser_actions_context_menu_min_padding = 0x7f07004f
com.example.wargani:id/activity_chooser_view_content = 0x7f090039
com.example.wargani:id/actions = 0x7f090038
com.example.wargani:id/action_text = 0x7f090037
com.example.wargani:id/action_mode_close_button = 0x7f090036
com.example.wargani:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f100158
com.example.wargani:styleable/ActivityFilter = 0x7f110006
com.example.wargani:attr/maxButtonHeight = 0x7f0400d9
com.example.wargani:id/action_mode_bar_stub = 0x7f090035
com.example.wargani:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f10006b
com.example.wargani:id/action_image = 0x7f090031
com.example.wargani:style/TextAppearance.Compat.Notification.Line2 = 0x7f100111
com.example.wargani:style/TextAppearance.AppCompat.Large = 0x7f1000ea
com.example.wargani:id/action_divider = 0x7f090030
com.example.wargani:id/action_context_bar = 0x7f09002f
com.example.wargani:id/end = 0x7f090060
com.example.wargani:attr/tickMarkTintMode = 0x7f040157
com.example.wargani:id/action_container = 0x7f09002e
com.example.wargani:attr/iconTintMode = 0x7f0400b2
com.example.wargani:id/normal = 0x7f090088
com.example.wargani:id/action_bar_spinner = 0x7f09002b
com.example.wargani:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f10004a
com.example.wargani:id/accessibility_custom_action_9 = 0x7f090026
com.example.wargani:drawable/abc_list_selector_holo_dark = 0x7f080034
com.example.wargani:id/accessibility_custom_action_7 = 0x7f090024
com.example.wargani:id/accessibility_custom_action_6 = 0x7f090023
com.example.wargani:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f1000a8
com.example.wargani:id/default_activity_button = 0x7f09005b
com.example.wargani:id/accessibility_custom_action_5 = 0x7f090022
com.example.wargani:string/call_notification_decline_action = 0x7f0f001e
com.example.wargani:id/accessibility_custom_action_31 = 0x7f090020
com.example.wargani:id/accessibility_custom_action_30 = 0x7f09001f
com.example.wargani:id/accessibility_custom_action_3 = 0x7f09001e
com.example.wargani:dimen/tooltip_vertical_padding = 0x7f070084
com.example.wargani:id/accessibility_custom_action_28 = 0x7f09001c
com.example.wargani:id/accessibility_custom_action_27 = 0x7f09001b
com.example.wargani:id/accessibility_custom_action_26 = 0x7f09001a
com.example.wargani:attr/queryPatterns = 0x7f040106
com.example.wargani:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f100106
com.example.wargani:id/accessibility_custom_action_25 = 0x7f090019
com.example.wargani:id/accessibility_custom_action_22 = 0x7f090016
com.example.wargani:id/accessibility_custom_action_19 = 0x7f090012
com.example.wargani:id/checkbox = 0x7f090050
com.example.wargani:id/accessibility_custom_action_14 = 0x7f09000d
com.example.wargani:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.example.wargani:id/accessibility_custom_action_1 = 0x7f090008
com.example.wargani:color/androidx_core_secondary_text_default_material_light = 0x7f06001c
com.example.wargani:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f100019
com.example.wargani:dimen/medium_text_size = 0x7f070068
com.example.wargani:id/SHIFT = 0x7f090004
com.example.wargani:id/META = 0x7f090003
com.example.wargani:layout/ime_base_split_test_activity = 0x7f0c0023
com.example.wargani:attr/activityName = 0x7f040023
com.example.wargani:id/FUNCTION = 0x7f090002
com.example.wargani:attr/iconSpaceReserved = 0x7f0400b0
com.example.wargani:id/CTRL = 0x7f090001
com.example.wargani:attr/logo = 0x7f0400d7
com.example.wargani:drawable/tooltip_frame_light = 0x7f080077
com.example.wargani:attr/fontWeight = 0x7f0400a7
com.example.wargani:attr/preferenceFragmentCompatStyle = 0x7f0400f9
com.example.wargani:attr/selectableItemBackground = 0x7f040115
com.example.wargani:drawable/notification_bg_normal = 0x7f08006d
com.example.wargani:drawable/abc_ic_star_black_16dp = 0x7f080021
com.example.wargani:drawable/notification_bg_low_normal = 0x7f08006b
com.example.wargani:style/Base.Widget.AppCompat.PopupMenu = 0x7f100090
com.example.wargani:drawable/notification_bg = 0x7f080069
com.example.wargani:drawable/launch_background = 0x7f080067
com.example.wargani:attr/paddingStart = 0x7f0400eb
com.example.wargani:drawable/ic_call_answer_video_low = 0x7f080064
com.example.wargani:drawable/abc_ic_search_api_material = 0x7f080020
com.example.wargani:layout/notification_template_custom_big = 0x7f0c0027
com.example.wargani:layout/custom_dialog = 0x7f0c001e
com.example.wargani:style/Widget.AppCompat.RatingBar.Small = 0x7f100172
com.example.wargani:drawable/ic_call_answer = 0x7f080061
com.example.wargani:dimen/abc_config_prefDialogWidth = 0x7f070017
com.example.wargani:drawable/abc_dialog_material_background = 0x7f080013
com.example.wargani:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f08005d
com.example.wargani:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f100161
com.example.wargani:attr/secondaryActivityAction = 0x7f04010f
com.example.wargani:drawable/btn_radio_on_mtrl = 0x7f08005c
com.example.wargani:drawable/btn_checkbox_checked_mtrl = 0x7f080056
com.example.wargani:attr/autoSizeStepGranularity = 0x7f040037
com.example.wargani:attr/actionBarItemBackground = 0x7f040001
com.example.wargani:color/notification_action_color_filter = 0x7f060048
com.example.wargani:attr/arrowHeadLength = 0x7f040031
com.example.wargani:drawable/abc_vector_test = 0x7f080055
com.example.wargani:attr/drawableRightCompat = 0x7f04007f
com.example.wargani:drawable/abc_textfield_activated_mtrl_alpha = 0x7f080050
com.example.wargani:drawable/abc_text_select_handle_right_mtrl_dark = 0x7f08004e
com.example.wargani:style/Widget.AppCompat.SeekBar = 0x7f100175
com.example.wargani:color/abc_tint_switch_track = 0x7f060018
com.example.wargani:drawable/abc_text_select_handle_left_mtrl_dark = 0x7f08004a
com.example.wargani:styleable/CheckBoxPreference = 0x7f110014
com.example.wargani:drawable/notification_tile_bg = 0x7f080073
com.example.wargani:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f10015e
com.example.wargani:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f10011f
com.example.wargani:drawable/abc_text_cursor_material = 0x7f080049
com.example.wargani:id/chronometer = 0x7f090052
com.example.wargani:drawable/abc_switch_thumb_material = 0x7f080045
com.example.wargani:styleable/View = 0x7f110041
com.example.wargani:string/abc_toolbar_collapse_description = 0x7f0f001a
com.example.wargani:layout/abc_alert_dialog_material = 0x7f0c0009
com.example.wargani:integer/config_tooltipAnimTime = 0x7f0a0003
com.example.wargani:drawable/abc_seekbar_tick_mark_material = 0x7f080041
com.example.wargani:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f10013c
com.example.wargani:style/Preference.Information = 0x7f1000ba
com.example.wargani:attr/windowNoTitle = 0x7f04017b
com.example.wargani:attr/singleLineTitle = 0x7f04011f
com.example.wargani:dimen/abc_control_inset_material = 0x7f070019
com.example.wargani:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f08003e
com.example.wargani:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f10001d
com.example.wargani:dimen/preference_seekbar_padding_horizontal = 0x7f07007a
com.example.wargani:drawable/abc_ratingbar_indicator_material = 0x7f080038
com.example.wargani:color/abc_tint_seek_thumb = 0x7f060016
com.example.wargani:layout/abc_dialog_title_material = 0x7f0c000c
com.example.wargani:attr/iconTint = 0x7f0400b1
com.example.wargani:dimen/abc_text_size_display_3_material = 0x7f070042
com.example.wargani:drawable/abc_list_selector_holo_light = 0x7f080035
com.example.wargani:attr/summary = 0x7f040138
com.example.wargani:attr/key = 0x7f0400bb
com.example.wargani:drawable/abc_seekbar_thumb_material = 0x7f080040
com.example.wargani:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f080057
com.example.wargani:drawable/notify_panel_notification_icon_bg = 0x7f080074
com.example.wargani:style/TextAppearance.AppCompat.Widget.Switch = 0x7f10010d
com.example.wargani:style/Widget.AppCompat.Spinner.Underlined = 0x7f10017a
com.example.wargani:drawable/abc_list_selector_disabled_holo_dark = 0x7f080032
com.example.wargani:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f080005
com.example.wargani:drawable/abc_list_selector_background_transition_holo_light = 0x7f080031
com.example.wargani:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f08000b
com.example.wargani:drawable/ic_arrow_down_24dp = 0x7f080060
com.example.wargani:attr/selectableItemBackgroundBorderless = 0x7f040116
com.example.wargani:drawable/abc_list_pressed_holo_light = 0x7f08002f
com.example.wargani:style/Widget.AppCompat.Button.Borderless = 0x7f100142
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f100086
com.example.wargani:attr/contentInsetEndWithActions = 0x7f040063
com.example.wargani:attr/actionBarSize = 0x7f040003
com.example.wargani:drawable/abc_list_pressed_holo_dark = 0x7f08002e
com.example.wargani:dimen/abc_dialog_corner_radius_material = 0x7f07001b
com.example.wargani:drawable/abc_ic_voice_search_api_material = 0x7f080027
com.example.wargani:drawable/abc_ic_star_half_black_48dp = 0x7f080026
com.example.wargani:drawable/abc_list_divider_material = 0x7f08002a
com.example.wargani:style/Base.TextAppearance.AppCompat.Body2 = 0x7f10000f
com.example.wargani:attr/layout_insetEdge = 0x7f0400c5
com.example.wargani:drawable/abc_ic_star_half_black_36dp = 0x7f080025
com.example.wargani:attr/borderlessButtonStyle = 0x7f04003f
com.example.wargani:id/progress_horizontal = 0x7f090094
com.example.wargani:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f08001e
com.example.wargani:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f08001b
com.example.wargani:styleable/Preference = 0x7f11002c
com.example.wargani:attr/actionViewClass = 0x7f040020
com.example.wargani:attr/allowDividerBelow = 0x7f04002b
com.example.wargani:layout/preference_widget_switch_compat = 0x7f0c003a
com.example.wargani:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f080016
com.example.wargani:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f100043
com.example.wargani:drawable/abc_ic_ab_back_material = 0x7f080015
com.example.wargani:style/Widget.AppCompat.Light.ActionBar = 0x7f100150
com.example.wargani:attr/fastScrollVerticalTrackDrawable = 0x7f040097
com.example.wargani:dimen/abc_text_size_headline_material = 0x7f070044
com.example.wargani:drawable/abc_edit_text_material = 0x7f080014
com.example.wargani:attr/subtitleTextStyle = 0x7f040136
com.example.wargani:drawable/abc_control_background_material = 0x7f080012
com.example.wargani:style/Base.TextAppearance.AppCompat.Title = 0x7f100026
com.example.wargani:attr/switchTextOff = 0x7f040141
com.example.wargani:id/accessibility_custom_action_21 = 0x7f090015
com.example.wargani:attr/trackTint = 0x7f04016a
com.example.wargani:id/accessibility_custom_action_10 = 0x7f090009
com.example.wargani:drawable/abc_cab_background_top_material = 0x7f080010
com.example.wargani:drawable/abc_cab_background_internal_bg = 0x7f08000f
com.example.wargani:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f100089
com.example.wargani:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f08000c
com.example.wargani:drawable/abc_btn_radio_material_anim = 0x7f08000a
com.example.wargani:drawable/abc_btn_colored_material = 0x7f080007
com.example.wargani:style/Base.ThemeOverlay.AppCompat.Light = 0x7f100051
com.example.wargani:color/browser_actions_bg_grey = 0x7f060029
com.example.wargani:color/black_text = 0x7f060022
com.example.wargani:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f080006
com.example.wargani:drawable/abc_btn_check_material_anim = 0x7f080004
com.example.wargani:attr/actionModeShareDrawable = 0x7f040019
com.example.wargani:drawable/abc_btn_borderless_material = 0x7f080002
com.example.wargani:dimen/fastscroll_default_thickness = 0x7f070059
com.example.wargani:attr/listDividerAlertDialog = 0x7f0400cb
com.example.wargani:attr/drawableSize = 0x7f040080
com.example.wargani:dimen/notification_large_icon_width = 0x7f07006e
com.example.wargani:attr/layout = 0x7f0400bf
com.example.wargani:drawable/abc_action_bar_item_background_material = 0x7f080001
com.example.wargani:attr/closeItemLayout = 0x7f040052
com.example.wargani:attr/dropDownListViewStyle = 0x7f040086
com.example.wargani:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f080000
com.example.wargani:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f100021
com.example.wargani:color/material_grey_850 = 0x7f060046
com.example.wargani:dimen/notification_right_side_padding_top = 0x7f070072
com.example.wargani:dimen/preference_seekbar_value_minWidth = 0x7f07007c
com.example.wargani:attr/multiChoiceItemLayout = 0x7f0400df
com.example.wargani:dimen/preference_dropdown_padding_start = 0x7f070078
com.example.wargani:dimen/notification_top_pad_large_text = 0x7f070077
com.example.wargani:layout/preference_dropdown = 0x7f0c002f
com.example.wargani:dimen/notification_small_icon_background_padding = 0x7f070073
com.example.wargani:id/tag_accessibility_heading = 0x7f0900c1
com.example.wargani:dimen/notification_subtext_size = 0x7f070075
com.example.wargani:color/highlighted_text_material_dark = 0x7f06003a
com.example.wargani:dimen/notification_large_icon_height = 0x7f07006d
com.example.wargani:dimen/notification_content_margin_start = 0x7f07006c
com.example.wargani:dimen/notification_big_circle_margin = 0x7f07006b
com.example.wargani:attr/dropdownPreferenceStyle = 0x7f040088
com.example.wargani:drawable/abc_tab_indicator_material = 0x7f080047
com.example.wargani:attr/colorSwitchThumbNormal = 0x7f04005f
com.example.wargani:dimen/hint_alpha_material_light = 0x7f070061
com.example.wargani:dimen/preferences_header_width = 0x7f07007e
com.example.wargani:dimen/notification_action_icon_size = 0x7f070069
com.example.wargani:attr/tag = 0x7f040143
com.example.wargani:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f070065
com.example.wargani:id/item_touch_helper_previous_elevation = 0x7f09007b
com.example.wargani:dimen/huge_text_size = 0x7f070064
com.example.wargani:styleable/Capability = 0x7f110013
com.example.wargani:id/text = 0x7f0900cc
com.example.wargani:attr/textAllCaps = 0x7f040144
com.example.wargani:dimen/hint_alpha_material_dark = 0x7f070060
com.example.wargani:style/Base.Widget.AppCompat.Button.Small = 0x7f100078
com.example.wargani:dimen/highlight_alpha_material_dark = 0x7f07005e
com.example.wargani:attr/splitMinHeightDp = 0x7f040127
com.example.wargani:dimen/fastscroll_margin = 0x7f07005a
com.example.wargani:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f100115
com.example.wargani:drawable/ic_call_answer_video = 0x7f080063
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f100029
com.example.wargani:animator/fragment_close_enter = 0x7f020000
com.example.wargani:style/TextAppearance.AppCompat.Button = 0x7f1000e2
com.example.wargani:attr/textLocale = 0x7f04014f
com.example.wargani:dimen/compat_notification_large_icon_max_width = 0x7f070056
com.example.wargani:dimen/compat_notification_large_icon_max_height = 0x7f070055
com.example.wargani:attr/windowMinWidthMajor = 0x7f040179
com.example.wargani:style/Widget.AppCompat.ActionButton.Overflow = 0x7f10013d
com.example.wargani:dimen/abc_text_size_title_material_toolbar = 0x7f07004d
com.example.wargani:style/Preference.SwitchPreferenceCompat.Material = 0x7f1000c4
com.example.wargani:dimen/abc_text_size_subtitle_material_toolbar = 0x7f07004b
com.example.wargani:attr/tintMode = 0x7f040159
com.example.wargani:dimen/abc_action_bar_default_padding_start_material = 0x7f070004
com.example.wargani:dimen/abc_text_size_subhead_material = 0x7f07004a
com.example.wargani:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f080059
com.example.wargani:attr/disableDependentsState = 0x7f040076
com.example.wargani:bool/abc_action_bar_embed_tabs = 0x7f050000
com.example.wargani:dimen/abc_text_size_large_material = 0x7f070045
com.example.wargani:style/Widget.AppCompat.ListView.DropDown = 0x7f100169
com.example.wargani:layout/preference_widget_switch = 0x7f0c0039
com.example.wargani:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f08000d
com.example.wargani:id/notification_main_column = 0x7f09008a
com.example.wargani:dimen/abc_text_size_display_4_material = 0x7f070043
com.example.wargani:attr/finishPrimaryWithPlaceholder = 0x7f040098
com.example.wargani:drawable/abc_item_background_holo_dark = 0x7f080028
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1000fe
com.example.wargani:attr/title = 0x7f04015a
com.example.wargani:dimen/abc_action_button_min_width_material = 0x7f07000e
com.example.wargani:attr/panelMenuListTheme = 0x7f0400ee
com.example.wargani:dimen/abc_text_size_display_2_material = 0x7f070041
com.example.wargani:attr/displayOptions = 0x7f040077
com.example.wargani:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f08005b
com.example.wargani:drawable/abc_ic_clear_material = 0x7f080017
com.example.wargani:style/Base.TextAppearance.AppCompat.Display3 = 0x7f100014
com.example.wargani:id/search_button = 0x7f0900a4
com.example.wargani:dimen/abc_text_size_body_2_material = 0x7f07003d
com.example.wargani:style/Theme.AppCompat.Dialog.Alert = 0x7f100121
com.example.wargani:style/Base.TextAppearance.AppCompat.Small = 0x7f100022
com.example.wargani:attr/spinnerStyle = 0x7f040123
com.example.wargani:attr/buttonBarStyle = 0x7f040044
com.example.wargani:dimen/abc_text_size_body_1_material = 0x7f07003c
com.example.wargani:attr/drawableTint = 0x7f040082
com.example.wargani:dimen/abc_search_view_preferred_width = 0x7f070037
com.example.wargani:id/contentPanel = 0x7f090057
com.example.wargani:dimen/abc_progress_bar_height_material = 0x7f070035
com.example.wargani:dimen/abc_panel_menu_list_width = 0x7f070034
com.example.wargani:dimen/abc_list_item_padding_horizontal_material = 0x7f070033
com.example.wargani:style/Widget.AppCompat.ListView.Menu = 0x7f10016a
com.example.wargani:layout/abc_action_mode_close_item_material = 0x7f0c0005
com.example.wargani:drawable/abc_item_background_holo_light = 0x7f080029
com.example.wargani:id/always = 0x7f09003e
com.example.wargani:dimen/abc_edit_text_inset_top_material = 0x7f07002e
com.example.wargani:id/accessibility_custom_action_15 = 0x7f09000e
com.example.wargani:array/hide_fingerprint_instantly_prefixes = 0x7f030004
com.example.wargani:dimen/abc_control_padding_material = 0x7f07001a
com.example.wargani:dimen/abc_edit_text_inset_horizontal_material = 0x7f07002d
com.example.wargani:attr/textAppearanceLargePopupMenu = 0x7f040145
com.example.wargani:id/tag_on_receive_content_mime_types = 0x7f0900c5
com.example.wargani:dimen/abc_edit_text_inset_bottom_material = 0x7f07002c
com.example.wargani:dimen/abc_dropdownitem_icon_width = 0x7f070029
com.example.wargani:id/preferences_detail = 0x7f090090
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f100035
com.example.wargani:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f08000e
com.example.wargani:style/TextAppearance.AppCompat = 0x7f1000df
com.example.wargani:dimen/abc_switch_padding = 0x7f07003b
com.example.wargani:dimen/abc_dialog_padding_top_material = 0x7f070025
com.example.wargani:style/Platform.ThemeOverlay.AppCompat = 0x7f1000a7
com.example.wargani:style/Platform.V25.AppCompat.Light = 0x7f1000ad
com.example.wargani:dimen/abc_dialog_padding_material = 0x7f070024
com.example.wargani:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f08001a
com.example.wargani:id/progress_circular = 0x7f090093
com.example.wargani:attr/barLength = 0x7f04003e
com.example.wargani:dimen/abc_dialog_list_padding_top_no_title = 0x7f070021
com.example.wargani:attr/tooltipForegroundColor = 0x7f040166
com.example.wargani:drawable/ic_call_decline_low = 0x7f080066
com.example.wargani:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f070020
com.example.wargani:attr/allowStacking = 0x7f04002c
com.example.wargani:dimen/notification_right_icon_size = 0x7f070071
com.example.wargani:dimen/abc_search_view_preferred_height = 0x7f070036
com.example.wargani:attr/toolbarNavigationButtonStyle = 0x7f040164
com.example.wargani:dimen/abc_dialog_fixed_height_minor = 0x7f07001d
com.example.wargani:dimen/abc_dialog_fixed_height_major = 0x7f07001c
com.example.wargani:dimen/abc_cascading_menus_min_smallest_width = 0x7f070016
com.example.wargani:layout/fingerprint_dialog_layout = 0x7f0c0020
com.example.wargani:dimen/abc_alert_dialog_button_bar_height = 0x7f070010
com.example.wargani:attr/layout_keyline = 0x7f0400c6
com.example.wargani:animator/fragment_open_exit = 0x7f020005
com.example.wargani:attr/radioButtonStyle = 0x7f040107
com.example.wargani:dimen/abc_action_button_min_height_material = 0x7f07000d
com.example.wargani:attr/preferenceTheme = 0x7f0400ff
com.example.wargani:attr/colorPrimary = 0x7f04005d
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f1000d1
com.example.wargani:string/status_bar_notification_info_overflow = 0x7f0f003f
com.example.wargani:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f07000c
com.example.wargani:dimen/abc_action_bar_stacked_max_height = 0x7f070009
com.example.wargani:attr/buttonTintMode = 0x7f04004c
com.example.wargani:dimen/abc_action_bar_default_height_material = 0x7f070002
com.example.wargani:dimen/abc_action_bar_content_inset_with_nav = 0x7f070001
com.example.wargani:color/tooltip_background_light = 0x7f060060
com.example.wargani:styleable/SplitPairFilter = 0x7f110037
com.example.wargani:color/tooltip_background_dark = 0x7f06005f
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f1000d5
com.example.wargani:color/switch_thumb_normal_material_dark = 0x7f06005d
com.example.wargani:attr/switchTextAppearance = 0x7f040140
com.example.wargani:id/fill = 0x7f090063
com.example.wargani:color/switch_thumb_material_light = 0x7f06005c
com.example.wargani:string/generic_error_no_keyguard = 0x7f0f0035
com.example.wargani:drawable/abc_list_selector_background_transition_holo_dark = 0x7f080030
com.example.wargani:color/switch_thumb_disabled_material_dark = 0x7f060059
com.example.wargani:dimen/abc_text_size_title_material = 0x7f07004c
com.example.wargani:attr/positiveButtonText = 0x7f0400f5
com.example.wargani:attr/fragment = 0x7f0400a8
com.example.wargani:color/secondary_text_default_material_dark = 0x7f060055
com.example.wargani:color/primary_text_default_material_light = 0x7f060050
com.example.wargani:color/primary_material_light = 0x7f06004e
com.example.wargani:color/abc_primary_text_disable_only_material_light = 0x7f06000a
com.example.wargani:color/primary_dark_material_light = 0x7f06004c
com.example.wargani:attr/paddingBottomNoButtons = 0x7f0400e9
com.example.wargani:color/primary_dark_material_dark = 0x7f06004b
com.example.wargani:string/copy = 0x7f0f0025
com.example.wargani:id/action_bar_title = 0x7f09002d
com.example.wargani:attr/ratingBarStyle = 0x7f040108
com.example.wargani:id/accessibility_custom_action_2 = 0x7f090013
com.example.wargani:drawable/abc_ic_menu_overflow_material = 0x7f08001c
com.example.wargani:bool/abc_allow_stacked_button_bar = 0x7f050001
com.example.wargani:id/transition_layout_save = 0x7f0900d8
com.example.wargani:color/preference_fallback_accent_color = 0x7f06004a
com.example.wargani:color/notification_icon_bg_color = 0x7f060049
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f100083
com.example.wargani:attr/actionModeWebSearchDrawable = 0x7f04001c
com.example.wargani:drawable/btn_radio_off_mtrl = 0x7f08005a
com.example.wargani:styleable/AppCompatTextView = 0x7f11000f
com.example.wargani:color/material_grey_800 = 0x7f060045
com.example.wargani:attr/drawableLeftCompat = 0x7f04007e
com.example.wargani:drawable/notification_action_background = 0x7f080068
com.example.wargani:color/material_grey_300 = 0x7f060042
com.example.wargani:attr/tooltipText = 0x7f040168
com.example.wargani:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1000f3
com.example.wargani:drawable/abc_list_longpressed_holo = 0x7f08002d
com.example.wargani:attr/titleTextStyle = 0x7f040163
com.example.wargani:color/material_deep_teal_500 = 0x7f060040
com.example.wargani:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f08001f
com.example.wargani:attr/fontProviderAuthority = 0x7f04009e
com.example.wargani:color/material_blue_grey_900 = 0x7f06003d
com.example.wargani:color/error_color_material_dark = 0x7f060035
com.example.wargani:color/ripple_material_dark = 0x7f060053
com.example.wargani:color/dim_foreground_material_light = 0x7f060034
com.example.wargani:attr/maxWidth = 0x7f0400db
com.example.wargani:id/bottomToTop = 0x7f090046
com.example.wargani:color/call_notification_answer_color = 0x7f06002f
com.example.wargani:string/abc_menu_ctrl_shortcut_label = 0x7f0f0009
com.example.wargani:color/button_material_light = 0x7f06002e
com.example.wargani:attr/autoSizeMinTextSize = 0x7f040035
com.example.wargani:color/button_material_dark = 0x7f06002d
com.example.wargani:string/abc_searchview_description_submit = 0x7f0f0016
com.example.wargani:style/Preference.CheckBoxPreference.Material = 0x7f1000b3
com.example.wargani:id/save_non_transition_alpha = 0x7f09009c
com.example.wargani:dimen/abc_text_size_menu_header_material = 0x7f070047
com.example.wargani:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f10014d
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f100104
com.example.wargani:color/browser_actions_divider_color = 0x7f06002a
com.example.wargani:color/bright_foreground_material_light = 0x7f060028
com.example.wargani:attr/preferenceStyle = 0x7f0400fe
com.example.wargani:color/bright_foreground_inverse_material_light = 0x7f060026
com.example.wargani:drawable/tooltip_frame_dark = 0x7f080076
com.example.wargani:attr/dialogMessage = 0x7f040071
com.example.wargani:attr/initialActivityCount = 0x7f0400b6
com.example.wargani:color/bright_foreground_disabled_material_light = 0x7f060024
com.example.wargani:color/background_material_light = 0x7f060020
com.example.wargani:attr/min = 0x7f0400de
com.example.wargani:anim/abc_slide_in_top = 0x7f010007
com.example.wargani:style/ThemeOverlay.AppCompat = 0x7f10012d
com.example.wargani:attr/font = 0x7f04009c
com.example.wargani:dimen/compat_button_padding_vertical_material = 0x7f070053
com.example.wargani:color/accent_material_dark = 0x7f060019
com.example.wargani:id/action_menu_divider = 0x7f090032
com.example.wargani:color/browser_actions_title_color = 0x7f06002c
com.example.wargani:id/textSpacerNoTitle = 0x7f0900cf
com.example.wargani:color/androidx_core_ripple_material_light = 0x7f06001b
com.example.wargani:string/fingerprint_dialog_touch_sensor = 0x7f0f002c
com.example.wargani:color/abc_tint_edittext = 0x7f060015
com.example.wargani:color/abc_tint_default = 0x7f060014
com.example.wargani:color/abc_secondary_text_material_dark = 0x7f060011
com.example.wargani:color/abc_search_url_text = 0x7f06000d
com.example.wargani:attr/listItemLayout = 0x7f0400cc
com.example.wargani:color/abc_primary_text_material_dark = 0x7f06000b
com.example.wargani:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f08001d
com.example.wargani:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f10011c
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f100101
com.example.wargani:color/secondary_text_disabled_material_dark = 0x7f060057
com.example.wargani:color/abc_primary_text_disable_only_material_dark = 0x7f060009
com.example.wargani:style/Base.TextAppearance.AppCompat.Display1 = 0x7f100012
com.example.wargani:dimen/abc_button_padding_horizontal_material = 0x7f070014
com.example.wargani:style/Widget.AppCompat.RatingBar.Indicator = 0x7f100171
com.example.wargani:id/accessibility_custom_action_0 = 0x7f090007
com.example.wargani:attr/textAppearanceSearchResultTitle = 0x7f04014b
com.example.wargani:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.example.wargani:attr/contentInsetStartWithNavigation = 0x7f040067
com.example.wargani:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f07000b
com.example.wargani:color/abc_hint_foreground_material_dark = 0x7f060007
com.example.wargani:color/material_grey_100 = 0x7f060041
com.example.wargani:attr/fontProviderQuery = 0x7f0400a3
com.example.wargani:color/abc_decor_view_status_guard = 0x7f060005
com.example.wargani:color/abc_btn_colored_text_material = 0x7f060003
com.example.wargani:string/abc_search_hint = 0x7f0f0012
com.example.wargani:color/abc_background_cache_hint_selector_material_light = 0x7f060001
com.example.wargani:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f10001f
com.example.wargani:layout/abc_action_mode_bar = 0x7f0c0004
com.example.wargani:dimen/abc_floating_window_z = 0x7f07002f
com.example.wargani:color/abc_background_cache_hint_selector_material_dark = 0x7f060000
com.example.wargani:attr/autoSizePresetSizes = 0x7f040036
com.example.wargani:bool/com.crashlytics.RequireBuildId = 0x7f050003
com.example.wargani:attr/navigationMode = 0x7f0400e2
com.example.wargani:attr/actionModeSelectAllDrawable = 0x7f040018
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1000fd
com.example.wargani:attr/itemPadding = 0x7f0400ba
com.example.wargani:attr/windowFixedHeightMinor = 0x7f040176
com.example.wargani:style/Base.V22.Theme.AppCompat = 0x7f100057
com.example.wargani:attr/windowFixedHeightMajor = 0x7f040175
com.example.wargani:style/Widget.AppCompat.SearchView.ActionBar = 0x7f100174
com.example.wargani:color/material_deep_teal_200 = 0x7f06003f
com.example.wargani:attr/navigationIcon = 0x7f0400e1
com.example.wargani:attr/buttonCompat = 0x7f040045
com.example.wargani:color/primary_material_dark = 0x7f06004d
com.example.wargani:attr/imageButtonStyle = 0x7f0400b4
com.example.wargani:attr/singleChoiceItemLayout = 0x7f04011e
com.example.wargani:attr/windowActionModeOverlay = 0x7f040174
com.example.wargani:style/TextAppearance.AppCompat.Subhead = 0x7f1000f7
com.example.wargani:string/abc_searchview_description_query = 0x7f0f0014
com.example.wargani:color/abc_search_url_text_selected = 0x7f060010
com.example.wargani:attr/windowActionBar = 0x7f040172
com.example.wargani:string/abc_activity_chooser_view_see_all = 0x7f0f0004
com.example.wargani:drawable/abc_btn_check_material = 0x7f080003
com.example.wargani:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0b0005
com.example.wargani:attr/viewInflaterClass = 0x7f04016f
com.example.wargani:layout/preference_recyclerview = 0x7f0c0035
com.example.wargani:dimen/abc_text_size_button_material = 0x7f07003e
com.example.wargani:attr/useSimpleSummaryProvider = 0x7f04016e
com.example.wargani:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f100149
com.example.wargani:attr/trackTintMode = 0x7f04016b
com.example.wargani:drawable/abc_btn_default_mtrl_shape = 0x7f080008
com.example.wargani:attr/track = 0x7f040169
com.example.wargani:style/Base.V23.Theme.AppCompat.Light = 0x7f10005a
com.example.wargani:style/Preference.SeekBarPreference = 0x7f1000bf
com.example.wargani:attr/titleTextColor = 0x7f040162
com.example.wargani:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f1000d3
com.example.wargani:attr/thumbTint = 0x7f040153
com.example.wargani:attr/titleMarginTop = 0x7f04015f
com.example.wargani:id/accessibility_custom_action_8 = 0x7f090025
com.example.wargani:color/call_notification_decline_color = 0x7f060030
com.example.wargani:attr/listLayout = 0x7f0400cd
com.example.wargani:attr/buttonIconDimen = 0x7f040047
com.example.wargani:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f070006
com.example.wargani:style/Base.Widget.AppCompat.Button = 0x7f100073
com.example.wargani:attr/titleMarginStart = 0x7f04015e
com.example.wargani:attr/listChoiceBackgroundIndicator = 0x7f0400c8
com.example.wargani:layout/abc_action_bar_title_item = 0x7f0c0000
com.example.wargani:drawable/abc_tab_indicator_mtrl_alpha = 0x7f080048
com.example.wargani:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f10002e
com.example.wargani:attr/titleMargin = 0x7f04015b
com.example.wargani:attr/windowFixedWidthMinor = 0x7f040178
com.example.wargani:attr/queryHint = 0x7f040105
com.example.wargani:attr/buttonGravity = 0x7f040046
com.example.wargani:attr/tickMark = 0x7f040155
com.example.wargani:style/Widget.AppCompat.PopupMenu = 0x7f10016b
com.example.wargani:attr/thumbTintMode = 0x7f040154
com.example.wargani:dimen/browser_actions_context_menu_max_width = 0x7f07004e
com.example.wargani:id/disableHome = 0x7f09005d
com.example.wargani:bool/abc_config_actionMenuItemAllCaps = 0x7f050002
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f100100
com.example.wargani:id/special_effects_controller_view_tag = 0x7f0900b4
com.example.wargani:style/PreferenceCategoryTitleTextStyle = 0x7f1000c5
com.example.wargani:attr/contentDescription = 0x7f040061
com.example.wargani:styleable/RecyclerView = 0x7f110033
com.example.wargani:style/TextAppearance.AppCompat.Widget.Button = 0x7f100105
com.example.wargani:style/Widget.AppCompat.ActionBar = 0x7f100136
com.example.wargani:attr/textAppearanceListItemSecondary = 0x7f040147
com.example.wargani:drawable/abc_ratingbar_small_material = 0x7f08003a
com.example.wargani:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f10017e
com.example.wargani:id/view_tree_view_model_store_owner = 0x7f0900e3
com.example.wargani:anim/abc_slide_in_bottom = 0x7f010006
com.example.wargani:attr/switchTextOn = 0x7f040142
com.example.wargani:color/material_grey_900 = 0x7f060047
com.example.wargani:attr/switchStyle = 0x7f04013f
com.example.wargani:attr/homeAsUpIndicator = 0x7f0400ad
com.example.wargani:id/accessibility_custom_action_18 = 0x7f090011
com.example.wargani:attr/navigationContentDescription = 0x7f0400e0
com.example.wargani:attr/showDividers = 0x7f04011a
com.example.wargani:style/PreferenceThemeOverlay = 0x7f1000cb
com.example.wargani:attr/switchPreferenceCompatStyle = 0x7f04013d
com.example.wargani:attr/buttonBarNegativeButtonStyle = 0x7f040041
com.example.wargani:attr/titleMarginBottom = 0x7f04015c
com.example.wargani:attr/switchPreferenceStyle = 0x7f04013e
com.example.wargani:dimen/abc_dialog_fixed_width_major = 0x7f07001e
com.example.wargani:attr/summaryOff = 0x7f040139
com.example.wargani:attr/drawableStartCompat = 0x7f040081
com.example.wargani:drawable/abc_ic_star_half_black_16dp = 0x7f080024
com.example.wargani:color/abc_decor_view_status_guard_light = 0x7f060006
com.example.wargani:attr/windowFixedWidthMajor = 0x7f040177
com.example.wargani:attr/suggestionRowLayout = 0x7f040137
com.example.wargani:styleable/AppCompatTheme = 0x7f110010
com.example.wargani:attr/textAppearanceListItem = 0x7f040146
com.example.wargani:style/TextAppearance.AppCompat.Caption = 0x7f1000e3
com.example.wargani:attr/spinnerDropDownItemStyle = 0x7f040122
com.example.wargani:attr/subtitleTextColor = 0x7f040135
com.example.wargani:drawable/abc_spinner_textfield_background_material = 0x7f080044
com.example.wargani:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f10015a
com.example.wargani:dimen/compat_button_inset_horizontal_material = 0x7f070050
com.example.wargani:drawable/abc_cab_background_top_mtrl_alpha = 0x7f080011
com.example.wargani:dimen/tooltip_horizontal_padding = 0x7f070080
com.example.wargani:string/call_notification_ongoing_text = 0x7f0f0021
com.example.wargani:attr/keylines = 0x7f0400bc
com.example.wargani:attr/preferenceFragmentListStyle = 0x7f0400fa
com.example.wargani:attr/popupMenuStyle = 0x7f0400f2
com.example.wargani:attr/titleMargins = 0x7f040160
com.example.wargani:id/tag_state_description = 0x7f0900c7
com.example.wargani:attr/searchHintIcon = 0x7f04010c
com.example.wargani:attr/preserveIconSpacing = 0x7f040100
com.example.wargani:attr/submitBackground = 0x7f040132
com.example.wargani:styleable/SeekBarPreference = 0x7f110035
com.example.wargani:color/secondary_text_default_material_light = 0x7f060056
com.example.wargani:attr/state_above_anchor = 0x7f04012e
com.example.wargani:attr/stackFromEnd = 0x7f04012d
com.example.wargani:attr/splitMinWidthDp = 0x7f040129
com.example.wargani:attr/splitMaxAspectRatioInLandscape = 0x7f040125
com.example.wargani:color/ripple_material_light = 0x7f060054
com.example.wargani:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f100080
com.example.wargani:styleable/PreferenceImageView = 0x7f110030
com.example.wargani:attr/listPreferredItemHeightLarge = 0x7f0400d1
com.example.wargani:attr/spinBars = 0x7f040121
com.example.wargani:attr/backgroundSplit = 0x7f04003a
com.example.wargani:dimen/notification_media_narrow_margin = 0x7f070070
com.example.wargani:attr/colorButtonNormal = 0x7f040058
com.example.wargani:styleable/FragmentContainerView = 0x7f11001f
com.example.wargani:attr/showSeekBarValue = 0x7f04011b
com.example.wargani:attr/fontProviderSystemFontFamily = 0x7f0400a4
com.example.wargani:style/Widget.AppCompat.ProgressBar = 0x7f10016e
com.example.wargani:id/transition_transform = 0x7f0900db
com.example.wargani:attr/seekBarPreferenceStyle = 0x7f040112
com.example.wargani:attr/activityChooserViewStyle = 0x7f040022
com.example.wargani:attr/actionBarTheme = 0x7f040009
com.example.wargani:attr/buttonPanelSideLayout = 0x7f040048
com.example.wargani:dimen/abc_list_item_height_material = 0x7f070031
com.example.wargani:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f1000d8
com.example.wargani:dimen/abc_action_button_min_width_overflow_material = 0x7f07000f
com.example.wargani:xml/ga_ad_services_config = 0x7f120003
com.example.wargani:attr/clearTop = 0x7f040050
com.example.wargani:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f100072
com.example.wargani:drawable/abc_switch_track_mtrl_alpha = 0x7f080046
com.example.wargani:attr/seekBarIncrement = 0x7f040111
com.example.wargani:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f100075
com.example.wargani:attr/stickyPlaceholder = 0x7f040130
com.example.wargani:attr/collapseIcon = 0x7f040054
com.example.wargani:dimen/tooltip_corner_radius = 0x7f07007f
com.example.wargani:attr/splitTrack = 0x7f04012b
com.example.wargani:dimen/abc_control_corner_material = 0x7f070018
com.example.wargani:styleable/ColorStateListItem = 0x7f110015
com.example.wargani:attr/splitRatio = 0x7f04012a
com.example.wargani:attr/preferenceCategoryStyle = 0x7f0400f6
com.example.wargani:attr/searchViewStyle = 0x7f04010e
com.example.wargani:attr/placeholderActivityName = 0x7f0400f1
com.example.wargani:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1000f6
com.example.wargani:attr/switchMinWidth = 0x7f04013b
com.example.wargani:styleable/DrawerArrowToggle = 0x7f11001a
com.example.wargani:attr/actionBarStyle = 0x7f040005
com.example.wargani:attr/searchIcon = 0x7f04010d
com.example.wargani:styleable/FontFamily = 0x7f11001c
com.example.wargani:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f100109
com.example.wargani:attr/primaryActivityName = 0x7f040101
com.example.wargani:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f100020
com.example.wargani:color/material_blue_grey_800 = 0x7f06003c
com.example.wargani:anim/abc_popup_enter = 0x7f010003
com.example.wargani:id/accessibility_custom_action_12 = 0x7f09000b
com.example.wargani:attr/actionDropDownStyle = 0x7f04000c
com.example.wargani:attr/toolbarStyle = 0x7f040165
com.example.wargani:attr/listPreferredItemPaddingEnd = 0x7f0400d3
com.example.wargani:attr/preferenceFragmentStyle = 0x7f0400fb
com.example.wargani:attr/preferenceCategoryTitleTextColor = 0x7f0400f8
com.example.wargani:attr/colorError = 0x7f04005c
com.example.wargani:attr/preferenceCategoryTitleTextAppearance = 0x7f0400f7
com.example.wargani:attr/popupWindowStyle = 0x7f0400f4
com.example.wargani:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f100128
com.example.wargani:color/abc_search_url_text_pressed = 0x7f06000f
com.example.wargani:id/accessibility_custom_action_24 = 0x7f090018
com.example.wargani:attr/actionBarTabBarStyle = 0x7f040006
com.example.wargani:attr/popupTheme = 0x7f0400f3
com.example.wargani:id/icon = 0x7f090074
com.example.wargani:attr/contentInsetRight = 0x7f040065
com.example.wargani:attr/showText = 0x7f04011c
com.example.wargani:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f100050
com.example.wargani:style/PreferenceFragmentList = 0x7f1000c8
com.example.wargani:dimen/abc_alert_dialog_button_dimen = 0x7f070011
com.example.wargani:attr/paddingTopNoTitle = 0x7f0400ec
com.example.wargani:style/TextAppearance.AppCompat.Body2 = 0x7f1000e1
com.example.wargani:bool/config_materialPreferenceIconSpaceReserved = 0x7f050004
com.example.wargani:id/expand_activities_button = 0x7f090061
com.example.wargani:attr/preferenceInformationStyle = 0x7f0400fc
com.example.wargani:string/abc_capital_on = 0x7f0f0007
com.example.wargani:attr/overlapAnchor = 0x7f0400e8
com.example.wargani:attr/defaultQueryHint = 0x7f04006b
com.example.wargani:attr/allowDividerAbove = 0x7f040029
com.example.wargani:attr/persistent = 0x7f0400f0
com.example.wargani:attr/order = 0x7f0400e6
com.example.wargani:id/SYM = 0x7f090005
com.example.wargani:id/bottom = 0x7f090045
com.example.wargani:attr/numericModifiers = 0x7f0400e5
com.example.wargani:style/PreferenceThemeOverlay.v14.Material = 0x7f1000cd
com.example.wargani:dimen/abc_action_bar_overflow_padding_end_material = 0x7f070007
com.example.wargani:attr/nestedScrollViewStyle = 0x7f0400e4
com.example.wargani:attr/fastScrollHorizontalTrackDrawable = 0x7f040095
com.example.wargani:color/dim_foreground_disabled_material_light = 0x7f060032
com.example.wargani:color/primary_text_disabled_material_light = 0x7f060052
com.example.wargani:attr/menu = 0x7f0400dd
com.example.wargani:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f080052
com.example.wargani:attr/autoCompleteTextViewStyle = 0x7f040033
com.example.wargani:attr/buttonBarButtonStyle = 0x7f040040
com.example.wargani:id/beginning = 0x7f090043
com.example.wargani:attr/buttonStyleSmall = 0x7f04004a
com.example.wargani:attr/closeIcon = 0x7f040051
com.example.wargani:attr/measureWithLargestChild = 0x7f0400dc
com.example.wargani:dimen/abc_text_size_menu_material = 0x7f070048
com.example.wargani:id/search_edit_frame = 0x7f0900a6
com.example.wargani:styleable/ActionMenuItemView = 0x7f110002
com.example.wargani:attr/layout_behavior = 0x7f0400c3
com.example.wargani:attr/textColorSearchUrl = 0x7f04014e
com.example.wargani:attr/coordinatorLayoutStyle = 0x7f040069
com.example.wargani:dimen/disabled_alpha_material_light = 0x7f070058
com.example.wargani:attr/drawableTintMode = 0x7f040083
com.example.wargani:attr/maxHeight = 0x7f0400da
com.example.wargani:attr/colorBackgroundFloating = 0x7f040057
com.example.wargani:dimen/abc_button_padding_vertical_material = 0x7f070015
com.example.wargani:attr/dialogPreferredPadding = 0x7f040073
com.example.wargani:attr/actionOverflowMenuStyle = 0x7f04001e
com.example.wargani:anim/abc_popup_exit = 0x7f010004
com.example.wargani:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f100087
com.example.wargani:drawable/notification_bg_normal_pressed = 0x7f08006e
com.example.wargani:attr/dialogIcon = 0x7f04006f
com.example.wargani:attr/entryValues = 0x7f040091
com.example.wargani:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f100144
com.example.wargani:attr/listPreferredItemPaddingRight = 0x7f0400d5
com.example.wargani:drawable/abc_spinner_mtrl_am_alpha = 0x7f080043
com.example.wargani:attr/fontProviderFetchTimeout = 0x7f0400a1
com.example.wargani:color/accent_material_light = 0x7f06001a
com.example.wargani:color/abc_tint_spinner = 0x7f060017
com.example.wargani:attr/subMenuArrow = 0x7f040131
com.example.wargani:attr/selectable = 0x7f040114
com.example.wargani:attr/checkedTextViewStyle = 0x7f04004f
com.example.wargani:attr/listPreferredItemPaddingLeft = 0x7f0400d4
com.example.wargani:attr/color = 0x7f040055
com.example.wargani:attr/showTitle = 0x7f04011d
com.example.wargani:attr/listPopupWindowStyle = 0x7f0400cf
com.example.wargani:color/material_blue_grey_950 = 0x7f06003e
com.example.wargani:id/accessibility_custom_action_16 = 0x7f09000f
com.example.wargani:string/abc_activitychooserview_choose_application = 0x7f0f0005
com.example.wargani:dimen/abc_action_bar_stacked_tab_max_width = 0x7f07000a
com.example.wargani:attr/ratingBarStyleIndicator = 0x7f040109
com.example.wargani:attr/listChoiceIndicatorMultipleAnimated = 0x7f0400c9
com.example.wargani:id/view_tree_lifecycle_owner = 0x7f0900e0
com.example.wargani:drawable/abc_text_select_handle_middle_mtrl_dark = 0x7f08004c
com.example.wargani:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.example.wargani:attr/lineHeight = 0x7f0400c7
com.example.wargani:string/call_notification_answer_action = 0x7f0f001c
com.example.wargani:dimen/fastscroll_minimum_range = 0x7f07005b
com.example.wargani:attr/titleMarginEnd = 0x7f04015d
com.example.wargani:attr/layout_anchorGravity = 0x7f0400c2
com.example.wargani:style/AlertDialog.AppCompat.Light = 0x7f100001
com.example.wargani:attr/alwaysExpand = 0x7f04002f
com.example.wargani:dimen/tooltip_margin = 0x7f070081
com.example.wargani:attr/actionMenuTextAppearance = 0x7f04000e
com.example.wargani:animator/fragment_open_enter = 0x7f020004
com.example.wargani:dimen/abc_list_item_height_small_material = 0x7f070032
com.example.wargani:attr/elevation = 0x7f04008d
com.example.wargani:array/assume_strong_biometrics_models = 0x7f030000
com.example.wargani:styleable/GradientColorItem = 0x7f110021
com.example.wargani:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f10006c
com.example.wargani:attr/checkboxStyle = 0x7f04004e
com.example.wargani:attr/layoutManager = 0x7f0400c0
com.example.wargani:style/Widget.AppCompat.Spinner = 0x7f100177
com.example.wargani:id/action_bar_root = 0x7f09002a
com.example.wargani:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f10008a
com.example.wargani:attr/fastScrollHorizontalThumbDrawable = 0x7f040094
com.example.wargani:attr/goIcon = 0x7f0400aa
com.example.wargani:layout/abc_expanded_menu_layout = 0x7f0c000d
com.example.wargani:attr/lStar = 0x7f0400bd
com.example.wargani:style/Base.Widget.AppCompat.Button.Colored = 0x7f100077
com.example.wargani:drawable/ic_call_decline = 0x7f080065
com.example.wargani:style/Base.AlertDialog.AppCompat.Light = 0x7f100007
com.example.wargani:dimen/abc_dropdownitem_text_padding_right = 0x7f07002b
com.example.wargani:interpolator/fast_out_slow_in = 0x7f0b0006
com.example.wargani:attr/isLightTheme = 0x7f0400b8
com.example.wargani:styleable/AppCompatSeekBar = 0x7f11000d
com.example.wargani:attr/collapseContentDescription = 0x7f040053
com.example.wargani:attr/initialExpandedChildrenCount = 0x7f0400b7
com.example.wargani:style/Widget.AppCompat.Toolbar = 0x7f10017d
com.example.wargani:attr/preferenceScreenStyle = 0x7f0400fd
com.example.wargani:layout/preference_material = 0x7f0c0034
com.example.wargani:attr/drawerArrowStyle = 0x7f040085
com.example.wargani:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f100045
com.example.wargani:layout/abc_activity_chooser_view = 0x7f0c0006
com.example.wargani:style/PreferenceThemeOverlay.v14 = 0x7f1000cc
com.example.wargani:dimen/preference_icon_minWidth = 0x7f070079
com.example.wargani:attr/editTextPreferenceStyle = 0x7f04008b
com.example.wargani:attr/indeterminateProgressStyle = 0x7f0400b5
com.example.wargani:attr/textAppearanceSmallPopupMenu = 0x7f04014c
com.example.wargani:attr/editTextBackground = 0x7f040089
com.example.wargani:attr/seekBarStyle = 0x7f040113
com.example.wargani:dimen/notification_small_icon_size_as_large = 0x7f070074
com.example.wargani:attr/homeLayout = 0x7f0400ae
com.example.wargani:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f100129
com.example.wargani:style/Base.TextAppearance.AppCompat.Headline = 0x7f100016
com.example.wargani:drawable/abc_text_select_handle_right_mtrl_light = 0x7f08004f
com.example.wargani:style/ThemeOverlay.AppCompat.Dark = 0x7f10012f
com.example.wargani:dimen/tooltip_precise_anchor_extra_offset = 0x7f070082
com.example.wargani:color/abc_primary_text_material_light = 0x7f06000c
com.example.wargani:dimen/notification_top_pad = 0x7f070076
com.example.wargani:attr/colorPrimaryDark = 0x7f04005e
com.example.wargani:attr/secondaryActivityName = 0x7f040110
com.example.wargani:drawable/abc_text_select_handle_left_mtrl_light = 0x7f08004b
com.example.wargani:attr/hideOnContentScroll = 0x7f0400ac
com.example.wargani:attr/colorControlHighlight = 0x7f04005a
com.example.wargani:attr/gapBetweenBars = 0x7f0400a9
com.example.wargani:string/confirm_device_credential_password = 0x7f0f0024
com.example.wargani:id/locale = 0x7f090081
com.example.wargani:attr/defaultValue = 0x7f04006c
com.example.wargani:id/radio = 0x7f090095
com.example.wargani:dimen/highlight_alpha_material_colored = 0x7f07005d
com.example.wargani:style/Theme.AppCompat.Dialog.MinWidth = 0x7f100122
com.example.wargani:id/start = 0x7f0900ba
com.example.wargani:attr/customNavigationLayout = 0x7f04006a
com.example.wargani:dimen/preference_seekbar_padding_vertical = 0x7f07007b
com.example.wargani:id/shortcut = 0x7f0900af
com.example.wargani:attr/fontVariationSettings = 0x7f0400a6
com.example.wargani:attr/background = 0x7f040039
com.example.wargani:drawable/notification_bg_low_pressed = 0x7f08006c
com.example.wargani:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f08003d
com.example.wargani:drawable/abc_ratingbar_material = 0x7f080039
com.example.wargani:attr/actionProviderClass = 0x7f04001f
com.example.wargani:drawable/abc_popup_background_mtrl_mult = 0x7f080037
com.example.wargani:style/Widget.AppCompat.Light.ActionButton = 0x7f100159
com.example.wargani:style/Theme.AppCompat.DayNight = 0x7f100119
com.example.wargani:dimen/abc_dialog_min_width_major = 0x7f070022
com.example.wargani:attr/reverseLayout = 0x7f04010b
com.example.wargani:attr/fontProviderFetchStrategy = 0x7f0400a0
com.example.wargani:attr/tickMarkTint = 0x7f040156
com.example.wargani:color/bright_foreground_material_dark = 0x7f060027
com.example.wargani:string/fallback_menu_item_copy_link = 0x7f0f0029
com.example.wargani:attr/finishSecondaryWithPrimary = 0x7f04009a
com.example.wargani:layout/select_dialog_singlechoice_material = 0x7f0c003d
com.example.wargani:attr/actionModeBackground = 0x7f040010
com.example.wargani:color/primary_text_default_material_dark = 0x7f06004f
com.example.wargani:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f10011d
com.example.wargani:id/tabMode = 0x7f0900be
com.example.wargani:attr/fastScrollEnabled = 0x7f040093
com.example.wargani:styleable/PopupWindowBackgroundState = 0x7f11002b
com.example.wargani:id/edit_query = 0x7f09005e
com.example.wargani:attr/enabled = 0x7f04008f
com.example.wargani:attr/thickness = 0x7f040151
com.example.wargani:style/Preference.DialogPreference.EditTextPreference = 0x7f1000b5
com.example.wargani:attr/logoDescription = 0x7f0400d8
com.example.wargani:attr/listMenuViewStyle = 0x7f0400ce
com.example.wargani:attr/editTextStyle = 0x7f04008c
com.example.wargani:style/Widget.AppCompat.ListMenuView = 0x7f100166
com.example.wargani:id/accessibility_custom_action_23 = 0x7f090017
com.example.wargani:attr/editTextColor = 0x7f04008a
com.example.wargani:attr/tint = 0x7f040158
com.example.wargani:style/Preference.DropDown.Material = 0x7f1000b9
com.example.wargani:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.example.wargani:attr/actionBarTabTextStyle = 0x7f040008
com.example.wargani:attr/fastScrollVerticalThumbDrawable = 0x7f040096
com.example.wargani:attr/actionModeFindDrawable = 0x7f040015
com.example.wargani:attr/drawableTopCompat = 0x7f040084
com.example.wargani:attr/switchPadding = 0x7f04013c
com.example.wargani:attr/showAsAction = 0x7f040119
com.example.wargani:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.example.wargani:attr/entries = 0x7f040090
com.example.wargani:style/Base.Widget.AppCompat.ActionBar = 0x7f100068
com.example.wargani:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f10004c
com.example.wargani:dimen/abc_text_size_display_1_material = 0x7f070040
com.example.wargani:layout/abc_cascading_menu_item_layout = 0x7f0c000b
com.example.wargani:attr/drawableEndCompat = 0x7f04007d
com.example.wargani:attr/actionLayout = 0x7f04000d
com.example.wargani:drawable/notification_template_icon_low_bg = 0x7f080072
com.example.wargani:color/abc_search_url_text_normal = 0x7f06000e
com.example.wargani:id/tag_unhandled_key_listeners = 0x7f0900ca
com.example.wargani:attr/orderingFromXml = 0x7f0400e7
com.example.wargani:array/crypto_fingerprint_fallback_vendors = 0x7f030002
com.example.wargani:attr/dividerVertical = 0x7f04007b
com.example.wargani:string/fallback_menu_item_open_in_browser = 0x7f0f002a
com.example.wargani:dimen/abc_text_size_medium_material = 0x7f070046
com.example.wargani:string/fingerprint_error_lockout = 0x7f0f002f
com.example.wargani:attr/dividerPadding = 0x7f04007a
com.example.wargani:attr/divider = 0x7f040078
com.example.wargani:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f080036
com.example.wargani:drawable/abc_seekbar_track_material = 0x7f080042
com.example.wargani:style/Base.V21.Theme.AppCompat = 0x7f100052
com.example.wargani:id/all = 0x7f09003d
com.example.wargani:attr/srcCompat = 0x7f04012c
com.example.wargani:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f10015b
com.example.wargani:style/Base.Widget.AppCompat.ProgressBar = 0x7f100093
com.example.wargani:layout/abc_list_menu_item_layout = 0x7f0c0010
com.example.wargani:anim/abc_tooltip_enter = 0x7f01000a
com.example.wargani:attr/splitLayoutDirection = 0x7f040124
com.example.wargani:styleable/SplitPlaceholderRule = 0x7f110039
com.example.wargani:attr/actionOverflowButtonStyle = 0x7f04001d
com.example.wargani:dimen/abc_list_item_height_large_material = 0x7f070030
com.example.wargani:attr/splitMaxAspectRatioInPortrait = 0x7f040126
com.example.wargani:color/primary_text_disabled_material_dark = 0x7f060051
com.example.wargani:attr/dialogTitle = 0x7f040075
com.example.wargani:layout/notification_template_icon_group = 0x7f0c0028
com.example.wargani:dimen/abc_button_inset_horizontal_material = 0x7f070012
com.example.wargani:attr/dialogLayout = 0x7f040070
com.example.wargani:string/fallback_menu_item_share_link = 0x7f0f002b
com.example.wargani:attr/animationBackgroundColor = 0x7f040030
com.example.wargani:attr/dependency = 0x7f04006d
com.example.wargani:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f080053
com.example.wargani:dimen/abc_action_bar_overflow_padding_start_material = 0x7f070008
com.example.wargani:color/switch_thumb_disabled_material_light = 0x7f06005a
com.example.wargani:styleable/ActionMenuView = 0x7f110003
com.example.wargani:id/tag_on_apply_window_listener = 0x7f0900c3
com.example.wargani:attr/subtitle = 0x7f040133
com.example.wargani:anim/abc_slide_out_bottom = 0x7f010008
com.example.wargani:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f080018
com.example.wargani:color/abc_color_highlight_material = 0x7f060004
com.example.wargani:dimen/abc_disabled_alpha_material_dark = 0x7f070027
com.example.wargani:attr/ratingBarStyleSmall = 0x7f04010a
com.example.wargani:color/error_color_material_light = 0x7f060036
com.example.wargani:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f10006f
com.example.wargani:string/summary_collapsed_preference_list = 0x7f0f0040
com.example.wargani:attr/actionBarTabStyle = 0x7f040007
com.example.wargani:color/abc_tint_btn_checkable = 0x7f060013
com.example.wargani:attr/queryBackground = 0x7f040104
com.example.wargani:drawable/abc_btn_radio_material = 0x7f080009
com.example.wargani:color/secondary_text_disabled_material_light = 0x7f060058
com.example.wargani:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.example.wargani:attr/height = 0x7f0400ab
com.example.wargani:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.example.wargani:attr/dividerHorizontal = 0x7f040079
com.example.wargani:attr/colorControlNormal = 0x7f04005b
com.example.wargani:attr/alertDialogStyle = 0x7f040027
com.example.wargani:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f10015c
com.example.wargani:attr/shouldDisableView = 0x7f040118
com.example.wargani:attr/colorControlActivated = 0x7f040059
com.example.wargani:attr/widgetLayout = 0x7f040171
com.example.wargani:attr/actionBarDivider = 0x7f040000
com.example.wargani:dimen/tooltip_y_offset_touch = 0x7f070086
com.example.wargani:id/clip_vertical = 0x7f090054
com.example.wargani:attr/dialogTheme = 0x7f040074
com.example.wargani:dimen/abc_action_bar_content_inset_material = 0x7f070000
com.example.wargani:attr/checkBoxPreferenceStyle = 0x7f04004d
com.example.wargani:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f100165
com.example.wargani:attr/buttonBarNeutralButtonStyle = 0x7f040042
com.example.wargani:attr/buttonTint = 0x7f04004b
com.example.wargani:style/Widget.AppCompat.ListView = 0x7f100168
com.example.wargani:id/accessibility_custom_action_4 = 0x7f090021
com.example.wargani:attr/buttonStyle = 0x7f040049
com.example.wargani:dimen/notification_main_column_padding_top = 0x7f07006f
com.example.wargani:id/action_bar_activity_content = 0x7f090028
com.example.wargani:attr/actionModeSplitBackground = 0x7f04001a
com.example.wargani:styleable/CompoundButton = 0x7f110016
com.example.wargani:attr/drawableBottomCompat = 0x7f04007c
com.example.wargani:attr/listChoiceIndicatorSingleAnimated = 0x7f0400ca
com.example.wargani:id/seekbar_value = 0x7f0900ad
com.example.wargani:id/action_bar_container = 0x7f090029
com.example.wargani:attr/theme = 0x7f040150
com.example.wargani:id/titleDividerNoCustom = 0x7f0900d2
com.example.wargani:id/accessibility_custom_action_11 = 0x7f09000a
com.example.wargani:layout/ime_secondary_split_test_activity = 0x7f0c0024
com.example.wargani:dimen/abc_button_inset_vertical_material = 0x7f070013
com.example.wargani:style/Theme.AppCompat.DayNight.Dialog = 0x7f10011b
com.example.wargani:style/Base.TextAppearance.AppCompat.Medium = 0x7f10001c
com.example.wargani:id/right_icon = 0x7f090099
com.example.wargani:string/google_crash_reporting_api_key = 0x7f0f0039
com.example.wargani:dimen/item_touch_helper_swipe_escape_velocity = 0x7f070067
com.example.wargani:drawable/abc_ic_star_black_48dp = 0x7f080023
com.example.wargani:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f10001b
com.example.wargani:drawable/abc_scrubber_track_mtrl_alpha = 0x7f08003f
com.example.wargani:id/tag_accessibility_actions = 0x7f0900bf
com.example.wargani:drawable/abc_text_select_handle_middle_mtrl_light = 0x7f08004d
com.example.wargani:style/Base.TextAppearance.AppCompat.Display4 = 0x7f100015
com.example.wargani:color/dim_foreground_disabled_material_dark = 0x7f060031
com.example.wargani:dimen/abc_dialog_title_divider_material = 0x7f070026
com.example.wargani:id/transition_current_scene = 0x7f0900d7
com.example.wargani:attr/expandActivityOverflowButtonDrawable = 0x7f040092
com.example.wargani:attr/textAppearanceSearchResultSubtitle = 0x7f04014a
com.example.wargani:attr/spanCount = 0x7f040120
com.example.wargani:color/material_grey_600 = 0x7f060044
com.example.wargani:attr/commitIcon = 0x7f040060
com.example.wargani:id/dialog_button = 0x7f09005c
com.example.wargani:attr/actionModeCopyDrawable = 0x7f040013
com.example.wargani:animator/fragment_fade_enter = 0x7f020002
com.example.wargani:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f10007b
com.example.wargani:attr/textAppearanceListItemSmall = 0x7f040148
com.example.wargani:attr/isPreferenceVisible = 0x7f0400b9
com.example.wargani:style/Base.V22.Theme.AppCompat.Light = 0x7f100058
com.example.wargani:id/topPanel = 0x7f0900d5
com.example.wargani:attr/colorAccent = 0x7f040056
com.example.wargani:attr/fontStyle = 0x7f0400a5
com.example.wargani:dimen/abc_seekbar_track_progress_height_material = 0x7f070039
com.example.wargani:id/seekbar = 0x7f0900ac
com.example.wargani:attr/adjustable = 0x7f040024
com.example.wargani:id/search_go_btn = 0x7f0900a7
com.example.wargani:attr/alpha = 0x7f04002d
com.example.wargani:attr/dialogCornerRadius = 0x7f04006e
com.example.wargani:drawable/fingerprint_dialog_fp_icon = 0x7f08005f
com.example.wargani:style/Platform.V25.AppCompat = 0x7f1000ac
com.example.wargani:attr/allowDividerAfterLastItem = 0x7f04002a
com.example.wargani:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f1000cf
com.example.wargani:style/Base.TextAppearance.AppCompat = 0x7f10000d
com.example.wargani:drawable/abc_list_divider_mtrl_alpha = 0x7f08002b
com.example.wargani:animator/fragment_fade_exit = 0x7f020003
com.example.wargani:id/action_bar = 0x7f090027
com.example.wargani:styleable/ActivityRule = 0x7f110007
com.example.wargani:attr/alertDialogCenterButtons = 0x7f040026
com.example.wargani:attr/actionModePasteDrawable = 0x7f040016
com.example.wargani:attr/actionBarSplitStyle = 0x7f040004
com.example.wargani:attr/backgroundTint = 0x7f04003c
com.example.wargani:attr/textColorAlertDialogListItem = 0x7f04014d
com.example.wargani:style/Widget.Compat.NotificationActionContainer = 0x7f10017f
com.example.wargani:style/Widget.AppCompat.ActionBar.TabText = 0x7f100139
com.example.wargani:attr/activityAction = 0x7f040021
com.example.wargani:attr/contentInsetEnd = 0x7f040062
com.example.wargani:attr/autoSizeMaxTextSize = 0x7f040034
com.example.wargani:attr/enableCopying = 0x7f04008e
com.example.wargani:attr/panelBackground = 0x7f0400ed
com.example.wargani:attr/fontProviderCerts = 0x7f04009f
com.example.wargani:dimen/tooltip_precise_anchor_threshold = 0x7f070083
com.example.wargani:id/accessibility_custom_action_29 = 0x7f09001d
com.example.wargani:attr/statusBarBackground = 0x7f04012f
com.example.wargani:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f070066
com.example.wargani:attr/actionModeCloseDrawable = 0x7f040012
com.example.wargani:attr/actionMenuTextColor = 0x7f04000f
com.example.wargani:id/textSpacerNoButtons = 0x7f0900ce
com.example.wargani:color/bright_foreground_disabled_material_dark = 0x7f060023
com.example.wargani:styleable/AnimatedStateListDrawableCompat = 0x7f110009
com.example.wargani:animator/fragment_close_exit = 0x7f020001
com.example.wargani:id/accessibility_custom_action_13 = 0x7f09000c
com.example.wargani:anim/abc_fade_in = 0x7f010000
com.example.wargani:layout/preference_widget_seekbar = 0x7f0c0037
com.example.wargani:attr/lastBaselineToBottomHeight = 0x7f0400be
com.example.wargani:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1000fc
com.example.wargani:array/crypto_fingerprint_fallback_prefixes = 0x7f030001
com.example.wargani:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f100071
com.example.wargani:id/showCustom = 0x7f0900b0
com.example.wargani:id/search_voice_btn = 0x7f0900ab
com.example.wargani:id/customPanel = 0x7f090059
com.example.wargani:dimen/abc_seekbar_track_background_height_material = 0x7f070038
com.example.wargani:dimen/compat_control_corner_material = 0x7f070054
com.example.wargani:attr/splitMinSmallestWidthDp = 0x7f040128
com.example.wargani:drawable/abc_list_focused_holo = 0x7f08002c
com.example.wargani:id/ALT = 0x7f090000
com.example.wargani:drawable/preference_list_divider_material = 0x7f080075
com.example.wargani:color/background_floating_material_dark = 0x7f06001d
com.example.wargani:id/search_plate = 0x7f0900a9
com.example.wargani:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.example.wargani:attr/listPreferredItemPaddingStart = 0x7f0400d6
com.example.wargani:attr/icon = 0x7f0400af
com.example.wargani:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0b0001
com.example.wargani:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.example.wargani:id/async = 0x7f090042
com.example.wargani:attr/alphabeticModifiers = 0x7f04002e
com.example.wargani:style/Base.V26.Theme.AppCompat = 0x7f10005b
com.example.wargani:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.example.wargani:style/Base.Widget.AppCompat.ListView = 0x7f10008d
com.example.wargani:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.example.wargani:anim/abc_tooltip_exit = 0x7f01000b
com.example.wargani:anim/abc_slide_out_top = 0x7f010009
com.example.wargani:color/grey_text = 0x7f060039
com.example.wargani:id/browser_actions_menu_items = 0x7f09004a
com.example.wargani:attr/textAppearancePopupMenuHeader = 0x7f040149
com.example.wargani:dimen/abc_dropdownitem_text_padding_left = 0x7f07002a
com.example.wargani:attr/layout_dodgeInsetEdges = 0x7f0400c4
com.example.wargani:attr/progressBarPadding = 0x7f040102
