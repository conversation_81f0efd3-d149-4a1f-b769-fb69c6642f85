androidx.appcompat.app.AlertController$RecycleListView
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements
androidx.recyclerview.widget.RecyclerView
com.google.firebase.sessions.SessionLifecycleService
androidx.appcompat.widget.Toolbar
androidx.preference.UnPressableLinearLayout
androidx.versionedparcelable.ParcelImpl
io.flutter.plugins.urllauncher.WebViewActivity
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty
com.google.firebase.components.ComponentDiscoveryService
androidx.appcompat.widget.SearchView
androidx.biometric.FingerprintDialogFragment
androidx.startup.InitializationProvider
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback
androidx.core.app.RemoteActionCompatParcelizer
com.google.firebase.installations.FirebaseInstallationsRegistrar
androidx.appcompat.widget.ActionMenuView
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin
androidx.browser.browseractions.BrowserActionsFallbackMenuView
net.nfet.flutter.printing.PrintingPlugin
androidx.biometric.BiometricFragment
androidx.appcompat.view.menu.ListMenuItemView
androidx.appcompat.widget.ActionBarContainer
io.flutter.embedding.engine.FlutterJNI
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
androidx.recyclerview.widget.LinearLayoutManager
androidx.appcompat.widget.FitWindowsLinearLayout
androidx.appcompat.widget.AlertDialogLayout
androidx.appcompat.view.menu.ExpandedMenuView
io.flutter.view.TextureRegistry$SurfaceTextureEntry
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper
androidx.fragment.app.DialogFragment
androidx.appcompat.widget.SwitchCompat
androidx.lifecycle.ReportFragment$LifecycleCallbacks
io.flutter.plugins.urllauncher.UrlLauncherPlugin
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
androidx.appcompat.widget.DialogTitle
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
com.google.firebase.datatransport.TransportRegistrar
com.google.firebase.provider.FirebaseInitProvider
com.google.android.datatransport.cct.CctBackendFactory
androidx.core.graphics.drawable.IconCompat
com.google.firebase.ktx.FirebaseCommonKtxRegistrar
com.google.android.gms.common.api.Scope
io.flutter.plugins.firebase.analytics.FlutterFirebaseAnalyticsPlugin
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar
androidx.profileinstaller.ProfileInstallerInitializer
androidx.lifecycle.SavedStateHandlesVM
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference
androidx.preference.PreferenceScreen
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback
androidx.biometric.BiometricViewModel
com.google.firebase.concurrent.ExecutorsRegistrar
android.support.v4.app.RemoteActionCompatParcelizer
com.google.firebase.analytics.FirebaseAnalytics
com.google.firebase.crashlytics.CrashlyticsRegistrar
io.flutter.plugins.firebase.core.FlutterFirebasePlugin
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback
androidx.core.graphics.drawable.IconCompatParcelizer
kotlinx.coroutines.internal.StackTraceRecoveryKt
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin
com.google.firebase.components.ComponentRegistrar
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
androidx.recyclerview.widget.StaggeredGridLayoutManager
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor
io.flutter.plugins.pathprovider.PathProviderPlugin
androidx.preference.TwoStatePreference
androidx.profileinstaller.ProfileInstallReceiver
dev.fluttercommunity.plus.share.SharePlusPendingIntent
io.flutter.view.TextureRegistry$GLTextureConsumer
com.google.firebase.FirebaseCommonRegistrar
com.google.android.gms.common.GooglePlayServicesIncorrectManifestValueException
androidx.preference.Preference
androidx.appcompat.widget.ActionBarOverlayLayout
androidx.preference.MultiSelectListPreference
androidx.lifecycle.ProcessLifecycleInitializer
io.flutter.plugins.firebase.crashlytics.FirebaseCrashlyticsTestCrash
com.google.android.gms.common.internal.ReflectedParcelable
io.flutter.view.AccessibilityViewEmbedder
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer
io.flutter.plugins.firebase.crashlytics.FlutterError
io.flutter.view.TextureRegistry$ImageTextureEntry
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin
com.tekartik.sqflite.SqflitePlugin
androidx.appcompat.widget.ContentFrameLayout
io.flutter.view.TextureRegistry$ImageConsumer
kotlin.coroutines.jvm.internal.BaseContinuationImpl
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin
com.google.firebase.sessions.FirebaseSessionsRegistrar
com.google.android.gms.measurement.AppMeasurement
com.google.firebase.FirebaseCommonKtxRegistrar
androidx.versionedparcelable.CustomVersionedParcelable
io.flutter.view.TextureRegistry$SurfaceProducer
com.google.android.gms.common.util.DynamiteApi
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader
androidx.preference.PreferenceCategory
androidx.preference.EditTextPreference
io.flutter.view.FlutterCallbackInformation
androidx.appcompat.widget.FitWindowsFrameLayout
androidx.lifecycle.ReportFragment
androidx.preference.internal.PreferenceImageView
androidx.core.app.CoreComponentFactory
androidx.window.extensions.core.util.function.Predicate
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements
androidx.appcompat.widget.ActionBarContextView
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseCrashlyticsPlugin
androidx.preference.DropDownPreference
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar
androidx.preference.CheckBoxPreference
io.flutter.plugin.platform.SingleViewPresentation
androidx.core.widget.NestedScrollView
androidx.lifecycle.DefaultLifecycleObserver
androidx.preference.SeekBarPreference
androidx.recyclerview.widget.GridLayoutManager
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry
androidx.preference.SwitchPreferenceCompat
io.flutter.plugin.text.ProcessTextPlugin
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation
androidx.preference.SwitchPreference
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService
com.example.wargani.MainActivity
com.google.android.gms.measurement.AppMeasurementReceiver
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
androidx.appcompat.widget.ActivityChooserView$InnerLayout
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements
androidx.preference.ListPreference
io.flutter.plugins.GeneratedPluginRegistrant
dev.fluttercommunity.plus.share.SharePlusPlugin
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry
androidx.annotation.Keep
androidx.appcompat.view.menu.ActionMenuItemView
io.flutter.plugins.imagepicker.ImagePickerPlugin
io.flutter.plugins.imagepicker.ImagePickerFileProvider
com.google.android.gms.measurement.AppMeasurementService
androidx.appcompat.widget.SearchView$SearchAutoComplete
androidx.preference.DialogPreference
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements
androidx.appcompat.widget.ButtonBarLayout
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
androidx.loader.app.LoaderManagerImpl$LoaderViewModel
androidx.appcompat.widget.ViewStubCompat
com.google.android.gms.common.api.internal.LifecycleCallback
androidx.transition.FragmentTransitionSupport
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException
com.google.android.gms.measurement.AppMeasurementJobService
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback
androidx.lifecycle.ProcessLifecycleOwner$attach$1
net.nfet.flutter.printing.PrintFileProvider
com.google.android.gms.common.GooglePlayServicesManifestException
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
io.flutter.embedding.engine.FlutterOverlaySurface
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar
android.support.v4.graphics.drawable.IconCompatParcelizer
androidx.window.extensions.core.util.function.Consumer
com.google.android.gms.common.annotation.KeepName
androidx.preference.PreferenceGroup
androidx.window.extensions.core.util.function.Function
dev.fluttercommunity.plus.share.ShareFileProvider
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback
com.baseflow.permissionhandler.PermissionHandlerPlugin
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
io.flutter.plugins.localauth.LocalAuthPlugin
androidx.core.app.RemoteActionCompat
kotlinx.coroutines.android.AndroidDispatcherFactory
com.google.android.gms.internal.measurement.zzfn$zzc: java.lang.String zzf
com.google.android.gms.internal.measurement.zzft$zzc: com.google.android.gms.internal.measurement.zzft$zzl zzh
com.google.android.gms.internal.measurement.zzft$zzn: java.lang.String zzg
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int workerCtl
com.google.android.gms.internal.measurement.zzgb$zzb: int zze
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzft$zzh zzbc
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mCreationTimestamp
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzfn$zza$zzb: int zze
androidx.appcompat.widget.SearchView$SavedState: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.PreferencesProto$Value: int BOOLEAN_FIELD_NUMBER
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_RESUME
io.flutter.view.AccessibilityViewEmbedder: android.view.View rootAccessibilityView
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean createNewReader
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzg
com.google.android.gms.internal.measurement.zzfh$zza: boolean zzi
com.google.android.gms.internal.measurement.zzfn$zza$zzf: java.lang.String zzg
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzbb
com.google.android.gms.internal.measurement.zzfn$zzc: boolean zzg
com.google.android.gms.internal.measurement.zzft$zzj: int zzg
com.google.android.gms.internal.measurement.zzfh$zze: java.lang.String zzg
com.google.android.gms.internal.measurement.zzft$zza: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zza: boolean zzi
com.google.android.gms.internal.measurement.zzfh$zzb: int zze
com.google.android.gms.internal.measurement.zzfh$zzb: boolean zzm
com.google.android.gms.internal.measurement.zzft$zzc: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfh$zze: int zzf
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle lifecycle
com.google.android.gms.internal.measurement.zzfn$zzb: boolean zzh
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzlp zzd
kotlinx.coroutines.UndispatchedCoroutine: boolean threadLocalIsSet
io.flutter.embedding.engine.FlutterOverlaySurface: int id
androidx.recyclerview.widget.RecyclerView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean notifiedDestroy
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean animating
com.google.android.gms.common.ConnectionResult: android.os.Parcelable$Creator CREATOR
com.google.firebase.datatransport.TransportRegistrar: java.lang.String LIBRARY_NAME
com.google.android.gms.internal.measurement.zzfh$zzc: java.lang.String zzi
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _delayed
com.google.android.gms.internal.measurement.zzft$zzk: com.google.android.gms.internal.measurement.zzft$zzk zzc
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: boolean mActive
com.google.android.gms.common.api.Scope: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfh$zzd: java.lang.String zzj
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: androidx.concurrent.futures.AbstractResolvableFuture$Waiter next
com.google.android.gms.internal.measurement.zzfh$zze: boolean zzi
com.google.android.gms.internal.measurement.zzfn$zzc: com.google.android.gms.internal.measurement.zzlp zzd
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _prev
com.google.android.gms.internal.measurement.zzft$zze: long zzh
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: int indexInArray
kotlinx.coroutines.EventLoopImplBase: int _isCompleted
com.google.android.gms.internal.measurement.zzgb$zzd: com.google.android.gms.internal.measurement.zzlp zzd
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar$Companion Companion
com.google.android.gms.common.zzo: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzf: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzft$zzj: long zzam
com.google.android.gms.internal.measurement.zzfn$zzh: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfn$zza$zzb: com.google.android.gms.internal.measurement.zzfn$zza$zzb zzc
com.google.android.gms.internal.measurement.zzfn$zzb: com.google.android.gms.internal.measurement.zzlp zzd
kotlinx.coroutines.internal.DispatchedContinuation: java.lang.Object _reusableCancellableContinuation
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MEMOIZED_SERIALIZED_SIZE_MASK
androidx.datastore.preferences.PreferencesProto$Value: int STRING_SET_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzft$zzc: int zzf
com.google.android.gms.internal.measurement.zzft$zzn: java.lang.String zzh
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean attached
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayList lastDequeuedImage
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzkc zzag
androidx.datastore.preferences.PreferencesProto$Value: int STRING_FIELD_NUMBER
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: java.util.concurrent.ExecutorService cachedThreadPool
io.flutter.embedding.engine.FlutterJNI: boolean initCalled
com.google.android.gms.internal.measurement.zzfh$zza: int zzf
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mOrigin
kotlinx.coroutines.android.AndroidExceptionPreHandler: java.lang.Object _preHandler
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: java.lang.String MODULE_ID
com.google.android.gms.internal.measurement.zzft$zzn: float zzj
com.google.android.gms.internal.measurement.zzft$zzj: long zzav
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean ignoringFence
com.google.android.gms.internal.measurement.zzfh$zzd: int zze
androidx.recyclerview.widget.StaggeredGridLayoutManager$LazySpanLookup$FullSpanItem: android.os.Parcelable$Creator CREATOR
androidx.appcompat.widget.Toolbar$SavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzl
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
com.google.android.gms.internal.measurement.zzft$zzi: java.lang.String zzh
kotlinx.coroutines.scheduling.WorkQueue: int producerIndex
com.google.android.gms.internal.measurement.zzjt: com.google.android.gms.internal.measurement.zzmw zzb
com.google.android.gms.internal.measurement.zzft$zzi: int zze
com.google.android.gms.internal.measurement.zzgb$zzd: com.google.android.gms.internal.measurement.zzgb$zzd zzc
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState state
com.google.android.gms.internal.measurement.zzft$zzc: boolean zzi
com.google.android.gms.internal.measurement.zzft$zzj: int zzay
com.google.android.gms.internal.measurement.zzfn$zze: com.google.android.gms.internal.measurement.zzfn$zze zzc
io.flutter.embedding.engine.FlutterJNI: float displayWidth
com.google.android.gms.internal.measurement.zzfh$zzb: int zzf
androidx.fragment.app.BackStackState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfn$zzh: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzn: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzkc zzbe
com.google.android.gms.common.zzq: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: float displayDensity
kotlinx.coroutines.android.HandlerDispatcherKt: android.view.Choreographer choreographer
io.flutter.plugin.platform.SingleViewPresentation: android.view.View$OnFocusChangeListener focusChangeListener
io.flutter.embedding.engine.FlutterJNI: boolean loadLibraryCalled
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback animationCallback
com.google.android.gms.internal.measurement.zzft$zzj: int zzs
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$InsetsListener insetsListener
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _prev
com.google.android.gms.common.zzs: android.os.Parcelable$Creator CREATOR
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Listener listeners
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzft$zzl zzc
com.google.android.gms.internal.measurement.zzgb$zzb: java.lang.String zzf
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzfn$zza zzc
androidx.core.widget.NestedScrollView$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.FlutterJNI: java.util.concurrent.locks.ReentrantReadWriteLock shellHolderLock
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: boolean released
com.google.android.gms.internal.measurement.zzfn$zze: int zzf
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTimedOutEventParams
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified backgroundDispatcher
com.google.android.gms.internal.measurement.zzfh$zza: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzh: java.lang.String zzf
kotlinx.coroutines.android.HandlerContext: kotlinx.coroutines.android.HandlerContext _immediate
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzfn$zza zzr
kotlinx.coroutines.internal.ConcurrentLinkedListNode: java.lang.Object _next
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mName
com.google.android.gms.internal.measurement.zzft$zzk: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTimedOutEventName
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzfn$zze zzs
com.google.android.gms.internal.measurement.zzfn$zza$zzf: java.lang.String zzf
com.google.android.gms.internal.measurement.zzft$zzn: double zzk
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzkc zzi
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzr
com.google.android.gms.internal.measurement.zzgb$zzd: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzbi
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzbf
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzjz zze
com.google.android.gms.internal.measurement.zzfh$zzc: int zze
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzkc zzh
kotlinx.coroutines.CancellableContinuationImpl: int _decisionAndIndex
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.PreferencesProto$StringSet DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzft$zzf: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfh$zze: com.google.android.gms.internal.measurement.zzfh$zzc zzh
com.google.android.gms.internal.measurement.zzft$zzj: int zzac
com.google.android.gms.internal.measurement.zzdq: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object bufferEndSegment
kotlinx.coroutines.CompletedExceptionally: int _handled
com.google.android.gms.internal.measurement.zzfn$zzf: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzb: com.google.android.gms.internal.measurement.zzfn$zzb zzc
com.google.android.gms.internal.measurement.zzfn$zzh: java.lang.String zzh
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzft$zzj zzc
com.google.android.gms.internal.measurement.zzhz: int zza
com.google.android.gms.internal.measurement.zzfn$zza$zzc: int zzf
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mAppId
kotlinx.coroutines.DispatchedCoroutine: int _decision
com.google.android.gms.internal.measurement.zzft$zzh: java.lang.String zzg
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzaw
com.google.android.gms.internal.measurement.zzft$zza: int zze
com.google.android.gms.internal.measurement.zzfh$zza: com.google.android.gms.internal.measurement.zzkc zzh
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.String TAG
io.flutter.embedding.engine.FlutterJNI: java.util.Set flutterUiDisplayListeners
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View view
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified transportFactory
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface surface
com.google.android.gms.internal.measurement.zzft$zzi: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzjt: int zzd
com.google.android.gms.internal.measurement.zzfn$zzd: int zzh
com.google.android.gms.internal.measurement.zzft$zzg: java.lang.String zzf
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzh
com.google.android.gms.internal.measurement.zzfh$zza: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzft$zzn: com.google.android.gms.internal.measurement.zzft$zzn zzc
com.google.android.gms.internal.measurement.zzfh$zze: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfh$zze: boolean zzk
com.google.android.gms.internal.measurement.zzfh$zzc: com.google.android.gms.internal.measurement.zzfh$zzd zzg
com.google.android.gms.internal.measurement.zzfn$zze: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzb: com.google.android.gms.internal.measurement.zzft$zzb zzc
com.google.android.gms.internal.measurement.zzfn$zzc: int zzi
com.google.android.gms.internal.measurement.zzfh$zze: int zze
com.google.android.gms.internal.measurement.zzfh$zzf: java.lang.String zzg
com.google.android.gms.internal.measurement.zzft$zzh: int zze
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzlp zzd
io.flutter.plugin.platform.SingleViewPresentation: android.widget.FrameLayout container
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mExpiredEventName
com.google.android.gms.internal.measurement.zzft$zzk: com.google.android.gms.internal.measurement.zzkc zzg
kotlinx.coroutines.flow.StateFlowSlot: java.lang.Object _state
io.flutter.plugin.platform.SingleViewPresentation: java.lang.String TAG
com.google.android.gms.measurement.internal.zzno: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastDequeueTime
com.google.android.gms.internal.measurement.zzfh$zzd: java.lang.String zzh
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] $VALUES
kotlinx.coroutines.flow.StateFlowImpl: java.lang.Object _state
androidx.fragment.app.BackStackRecordState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzi: java.lang.String zzg
com.google.android.gms.internal.measurement.zzfn$zzc: com.google.android.gms.internal.measurement.zzfn$zzc zzc
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_HASH_CODE
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture
com.google.android.gms.internal.measurement.zzgb$zzd: int zzf
com.google.android.gms.internal.measurement.zzfh$zza: boolean zzj
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzft$zzb zzbn
androidx.recyclerview.widget.LinearLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.ThreadSafeHeap: int _size
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.lang.String LIBRARY_NAME
androidx.concurrent.futures.AbstractResolvableFuture$Waiter: java.lang.Thread thread
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image image
com.google.android.gms.measurement.internal.zzac: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzj: long zzx
com.google.android.gms.internal.measurement.zzfn$zza$zzb: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.measurement.internal.zzn: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfh$zzb: boolean zzl
com.google.android.gms.internal.measurement.zzfn$zza$zzf: int zze
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzi
com.google.android.gms.internal.measurement.zzfh$zzc: com.google.android.gms.internal.measurement.zzfh$zzc zzc
com.google.android.gms.internal.measurement.zzfn$zzc: boolean zzh
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggerEventName
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List finalClippingPaths
io.flutter.plugin.platform.SingleViewPresentation: int viewId
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer this$0
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object receiveSegment
io.flutter.embedding.engine.FlutterJNI: java.lang.String vmServiceUri
com.google.android.gms.internal.measurement.zzft$zzg: com.google.android.gms.internal.measurement.zzkc zzk
com.google.android.gms.internal.measurement.zzft$zzj: long zzab
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback this$0
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzah
com.google.android.gms.internal.measurement.zzfh$zzc: boolean zzh
com.google.android.gms.internal.measurement.zzfh$zza: com.google.android.gms.internal.measurement.zzfh$zza zzc
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id
com.google.android.gms.internal.measurement.zzft$zzj: long zzj
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzfn$zzf zzu
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzao
kotlinx.coroutines.InvokeOnCancelling: int _invoked
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object head
com.google.android.gms.internal.measurement.zzfn$zza$zzb: int zzg
com.google.android.gms.internal.measurement.zzft$zzn: int zze
androidx.datastore.preferences.PreferencesProto$PreferenceMap: int PREFERENCES_FIELD_NUMBER
kotlinx.coroutines.DefaultExecutor: int debugStatus
com.google.android.gms.common.Feature: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzgb$zzd: java.lang.String zzh
kotlinx.coroutines.scheduling.WorkQueue: java.lang.Object lastScheduledTask
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzfn$zzd zzc
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzl
androidx.customview.view.AbsSavedState: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfh$zzc: com.google.android.gms.internal.measurement.zzlp zzd
androidx.datastore.preferences.protobuf.AbstractMessageLite: int memoizedHashCode
com.google.android.gms.internal.measurement.zzgb$zzc: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: int MODULE_VERSION
io.flutter.view.AccessibilityViewEmbedder: int nextFlutterId
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager deferredComponentManager
io.flutter.view.AccessibilityViewEmbedder: java.lang.String TAG
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzp
com.google.android.gms.internal.measurement.zzft$zzk: int zzf
com.google.android.gms.internal.measurement.zzft$zzm: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzd: java.lang.String zzq
io.flutter.view.AccessibilityViewEmbedder: java.util.Map originToFlutterId
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback callback
com.google.android.gms.internal.measurement.zzjt: java.util.Map zzc
androidx.lifecycle.ReportFragment$LifecycleCallbacks: androidx.lifecycle.ReportFragment$LifecycleCallbacks$Companion Companion
com.google.android.gms.internal.measurement.zzft$zzd: int zzf
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzk
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _rootCause
io.flutter.plugins.GeneratedPluginRegistrant: java.lang.String TAG
androidx.datastore.preferences.PreferencesProto$Value: java.lang.Object value_
kotlinx.coroutines.CancelledContinuation: int _resumed
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastScheduleTime
com.google.android.gms.internal.measurement.zzft$zzj: int zzbl
com.google.android.gms.internal.measurement.zzfn$zzf: java.lang.String zzf
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzf
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzj
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzh
com.google.android.gms.internal.measurement.zzfh$zzf: int zzf
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims
com.google.android.gms.internal.measurement.zzft$zzd: com.google.android.gms.internal.measurement.zzlp zzd
androidx.datastore.preferences.PreferencesProto$Value: int BYTES_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzft$zzg: com.google.android.gms.internal.measurement.zzlp zzd
kotlinx.coroutines.EventLoopImplBase: java.lang.Object _queue
com.google.android.gms.internal.measurement.zzfh$zzd: com.google.android.gms.internal.measurement.zzfh$zzd zzc
com.google.android.gms.internal.measurement.zzfn$zzd: boolean zzm
androidx.fragment.app.FragmentState: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedWidth
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzo
kotlinx.coroutines.scheduling.CoroutineScheduler: int _isTerminated
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.AccessibilityEventsDelegate accessibilityEventsDelegate
com.google.android.gms.internal.measurement.zzft$zzm: int zze
com.google.android.gms.internal.measurement.zzfh$zzd: boolean zzg
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzq
androidx.datastore.preferences.PreferencesProto$Value: int LONG_FIELD_NUMBER
io.flutter.view.AccessibilityViewEmbedder: java.util.Map embeddedViewToDisplayBounds
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.lang.Object lock
com.google.android.gms.internal.measurement.zzgb$zzd: int zze
com.google.android.gms.internal.measurement.zzft$zzc: com.google.android.gms.internal.measurement.zzft$zzl zzg
com.google.android.gms.internal.measurement.zzfh$zzf: com.google.android.gms.internal.measurement.zzfh$zzf zzc
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.dart.PlatformMessageHandler platformMessageHandler
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.Object mValue
com.google.android.gms.internal.measurement.zzfn$zza$zzc: int zzg
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Internal$ProtobufList strings_
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzj: long zzl
kotlinx.coroutines.channels.BufferedChannel: long bufferEnd
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float finalOpacity
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzi
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int deferredInsetTypes
com.google.android.gms.internal.measurement.zzft$zzm: int zzf
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_STOP
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long lastQueueTime
com.google.android.gms.internal.measurement.zzft$zze: com.google.android.gms.internal.measurement.zzkc zzf
com.google.android.gms.internal.measurement.zzft$zzh: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzfn$zzh zzt
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.PreferencesProto$PreferenceMap DEFAULT_INSTANCE
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified blockingDispatcher
kotlinx.coroutines.sync.MutexImpl: java.lang.Object owner
com.google.android.gms.internal.measurement.zzfh$zza: int zze
com.google.android.gms.internal.measurement.zzfh$zzb: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzg: java.lang.String zzf
androidx.concurrent.futures.AbstractResolvableFuture: androidx.concurrent.futures.AbstractResolvableFuture$Waiter waiters
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_CREATE
kotlinx.coroutines.internal.LockFreeTaskQueueCore: java.lang.Object _next
com.google.android.gms.internal.measurement.zzfn$zzd: int zze
com.google.android.gms.internal.measurement.zzgb$zzc: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zza: int zze
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker: java.lang.Object nextParkedWorker
kotlinx.coroutines.internal.LockFreeTaskQueueCore: long _state
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackClassName
com.google.android.gms.internal.measurement.zzft$zzj: int zzbo
kotlinx.coroutines.scheduling.CoroutineScheduler: long parkedWorkersStack
kotlinx.coroutines.DefaultExecutor: java.lang.Thread _thread
kotlinx.coroutines.scheduling.WorkQueue: int consumerIndex
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzar
kotlinx.coroutines.scheduling.WorkQueue: int blockingTasksInBuffer
com.google.android.gms.internal.measurement.zzft$zzj: long zzau
com.google.android.gms.internal.measurement.zzft$zzm: com.google.android.gms.internal.measurement.zzft$zzm zzc
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzu
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzaf
io.flutter.embedding.engine.FlutterJNI: android.os.Looper mainLooper
com.google.android.gms.internal.measurement.zzft$zze: com.google.android.gms.internal.measurement.zzft$zze zzc
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzae
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzaz
com.google.android.gms.internal.measurement.zzfh$zze: com.google.android.gms.internal.measurement.zzfh$zze zzc
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzt
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzkc zzj
com.google.android.gms.internal.measurement.zzfn$zzb: java.lang.String zzf
com.google.android.gms.internal.measurement.zzft$zzh: com.google.android.gms.internal.measurement.zzft$zza zzh
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzf
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean released
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate accessibilityDelegate
com.google.android.gms.internal.measurement.zzfn$zza$zzc: com.google.android.gms.internal.measurement.zzfn$zza$zzc zzc
com.google.android.gms.internal.measurement.zzfh$zzd: int zzf
androidx.datastore.preferences.PreferencesProto$Value: int DOUBLE_FIELD_NUMBER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int memoizedSerializedSize
androidx.concurrent.futures.AbstractResolvableFuture: java.lang.Object value
com.google.firebase.sessions.FirebaseSessionsRegistrar: java.lang.String LIBRARY_NAME
com.google.android.gms.measurement.internal.zzba: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzkc zzk
com.google.android.gms.internal.measurement.zzft$zzc: int zze
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.protobuf.Parser PARSER
com.google.android.gms.internal.measurement.zzgb$zzc: com.google.android.gms.internal.measurement.zzgb$zzc zzc
com.google.android.gms.internal.measurement.zzft$zzg: com.google.android.gms.internal.measurement.zzft$zzg zzc
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.MapFieldLite preferences_
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object closeHandler
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.ArrayDeque imageReaderQueue
androidx.fragment.app.FragmentManager$LaunchedFragmentInfo: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_DESTROY
com.google.android.gms.internal.measurement.zzfn$zzg: com.google.android.gms.internal.measurement.zzfn$zzg zzc
com.google.android.gms.internal.measurement.zzft$zzk: int zze
kotlinx.coroutines.sync.SemaphoreImpl: java.lang.Object tail
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate asyncWaitForVsyncDelegate
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzkc zzj
com.google.android.gms.internal.measurement.zzfn$zzh: int zze
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_START
kotlinx.coroutines.channels.BufferedChannel: long sendersAndCloseStatus
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: java.util.HashMap perImageReaders
com.google.android.gms.internal.measurement.zzft$zzf: int zze
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id
com.google.android.gms.internal.measurement.zzft$zzi: int zzi
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object sendSegment
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzjz zzf
com.google.android.gms.internal.measurement.zzfn$zzf: com.google.android.gms.internal.measurement.zzfn$zzf zzc
com.google.android.gms.internal.measurement.zzfh$zzf: com.google.android.gms.internal.measurement.zzkc zzi
com.google.android.gms.internal.measurement.zzft$zzj: int zzaj
kotlinx.coroutines.scheduling.CoroutineScheduler: long controlState
com.google.android.gms.internal.measurement.zzfh$zzc: com.google.android.gms.internal.measurement.zzfh$zzf zzf
com.google.android.gms.internal.measurement.zzft$zze: java.lang.String zzg
io.flutter.embedding.engine.FlutterJNI: java.lang.Long nativeShellHolderId
com.google.android.gms.common.internal.zzk: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzj: int zzak
com.google.android.gms.internal.measurement.zzfn$zza$zzc: int zze
io.flutter.embedding.engine.FlutterJNI: boolean prefetchDefaultFontManagerCalled
com.google.android.gms.internal.measurement.zzft$zze: int zze
com.google.android.gms.common.internal.GetServiceRequest: android.os.Parcelable$Creator CREATOR
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackLibraryPath
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: java.lang.Runnable onFrameConsumed
com.google.android.gms.internal.measurement.zzfh$zzb: boolean zzk
com.google.android.gms.internal.measurement.zzfn$zzg: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzj: long zzk
com.google.android.gms.internal.measurement.zzfh$zzb: com.google.android.gms.internal.measurement.zzfh$zzd zzj
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_PAUSE
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.localization.LocalizationPlugin localizationPlugin
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzax
com.google.android.gms.internal.measurement.zzfn$zzg: int zze
com.google.android.gms.internal.measurement.zzft$zzh: com.google.android.gms.internal.measurement.zzft$zzh zzc
com.google.android.gms.internal.measurement.zzft$zzd: com.google.android.gms.internal.measurement.zzft$zzd zzc
com.google.android.gms.internal.measurement.zzft$zzg: java.lang.String zzg
kotlinx.coroutines.JobSupport: java.lang.Object _state
androidx.datastore.preferences.protobuf.GeneratedMessageLite: androidx.datastore.preferences.protobuf.UnknownFieldSetLite unknownFields
com.google.android.gms.internal.measurement.zzgb$zzd: boolean zzj
androidx.datastore.preferences.PreferencesProto$StringSet: androidx.datastore.preferences.protobuf.Parser PARSER
io.flutter.plugin.platform.SingleViewPresentation: android.content.Context outerContext
com.google.android.gms.internal.measurement.zzft$zzj: int zzaq
com.google.android.gms.internal.measurement.zzgb$zzc: int zze
com.google.android.gms.internal.measurement.zzgb$zzd: java.lang.String zzi
androidx.appcompat.widget.AppCompatSpinner$SavedState: android.os.Parcelable$Creator CREATOR
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets lastWindowInsets
kotlinx.coroutines.internal.LimitedDispatcher: int runningWorkers
com.google.android.gms.internal.measurement.zzft$zzc: com.google.android.gms.internal.measurement.zzft$zzc zzc
com.google.android.gms.internal.measurement.zzfn$zzd: java.lang.String zzg
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List mutators
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzbj
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_IMAGES
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzkc zzn
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int requestedHeight
com.google.android.gms.internal.measurement.zzfn$zzc: int zze
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzbk
androidx.versionedparcelable.ParcelImpl: android.os.Parcelable$Creator CREATOR
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: java.lang.String TAG
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzad
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _removedRef
com.google.android.gms.internal.measurement.zzfh$zzb: boolean zzi
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzk
com.google.android.gms.internal.measurement.zzft$zze: int zzj
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: java.lang.String mTriggeredEventName
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzap
androidx.fragment.app.FragmentManagerState: android.os.Parcelable$Creator CREATOR
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: java.util.Map registeredPlugins
com.google.android.gms.internal.measurement.zzft$zzm: com.google.android.gms.internal.measurement.zzjz zzg
com.google.android.gms.internal.measurement.zzfn$zze: int zze
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController platformViewsController
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzz
com.google.android.gms.internal.measurement.zzft$zzj: int zzai
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzkc zzi
com.google.android.gms.internal.measurement.zzfn$zzh: com.google.android.gms.internal.measurement.zzfn$zzh zzc
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.FirebaseSessionsRegistrar$Companion Companion
com.google.android.gms.internal.measurement.zzfn$zza: com.google.android.gms.internal.measurement.zzkc zzh
kotlinx.coroutines.sync.SemaphoreImpl: long enqIdx
com.google.android.gms.internal.measurement.zzft$zzg: long zzh
kotlinx.coroutines.sync.SemaphoreImpl: long deqIdx
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzy
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix finalMatrix
kotlinx.coroutines.channels.BufferedChannel: long receivers
com.google.android.gms.internal.measurement.zzfn$zzf: java.lang.String zzg
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzbm
com.google.android.gms.internal.measurement.zzft$zzd: int zze
com.google.android.gms.internal.measurement.zzgb$zza: com.google.android.gms.internal.measurement.zzgb$zza zzc
com.google.android.gms.internal.measurement.zzfn$zzd: com.google.android.gms.internal.measurement.zzkc zzo
com.google.android.gms.internal.measurement.zzfn$zzd: java.lang.String zzp
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mExpiredEventParams
com.google.android.gms.internal.measurement.zzft$zzj: long zzan
com.google.android.gms.internal.measurement.zzft$zza: com.google.android.gms.internal.measurement.zzft$zza zzc
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean CLEANUP_ON_MEMORY_PRESSURE
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int MAX_DEQUEUED_IMAGES
io.flutter.plugin.platform.SingleViewPresentation: boolean startFocused
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzaa
androidx.recyclerview.widget.StaggeredGridLayoutManager$SavedState: android.os.Parcelable$Creator CREATOR
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event$Companion Companion
com.google.android.gms.internal.measurement.zzgb$zzb: com.google.android.gms.internal.measurement.zzgb$zzb zzc
com.google.android.gms.internal.measurement.zzfn$zza$zzf: com.google.android.gms.internal.measurement.zzfn$zza$zzf zzc
com.google.android.gms.internal.measurement.zzgb$zzd: double zzk
com.google.android.gms.internal.measurement.zzft$zze: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzb: int zze
com.google.android.gms.common.internal.ConnectionTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int UNINITIALIZED_SERIALIZED_SIZE
kotlinx.coroutines.internal.AtomicOp: java.lang.Object _consensus
androidx.datastore.preferences.PreferencesProto$Value: int FLOAT_FIELD_NUMBER
com.google.android.gms.internal.measurement.zzft$zzn: long zzf
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified firebaseInstallationsApi
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$AccessibilityDelegatingFrameLayout rootView
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar$Companion Companion
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: android.os.Bundle mTriggeredEventParams
com.google.android.gms.internal.measurement.zzfn$zzd: java.lang.String zzl
com.google.android.gms.internal.measurement.zzfh$zze: boolean zzj
com.google.android.gms.measurement.internal.zzmv: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzbd
com.google.android.gms.internal.measurement.zzfh$zzd: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.common.internal.RootTelemetryConfiguration: android.os.Parcelable$Creator CREATOR
kotlinx.coroutines.internal.LockFreeTaskQueue: java.lang.Object _cur
com.google.android.gms.internal.measurement.zzfh$zzb: com.google.android.gms.internal.measurement.zzkc zzh
kotlinx.coroutines.sync.SemaphoreImpl: int _availablePermits
com.google.android.gms.internal.measurement.zzft$zzf: long zzg
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event ON_ANY
com.google.android.gms.internal.measurement.zzft$zza: java.lang.String zzg
kotlinx.coroutines.JobSupport: java.lang.Object _parentHandle
kotlinx.coroutines.internal.ResizableAtomicArray: java.util.concurrent.atomic.AtomicReferenceArray array
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _state
com.google.android.gms.internal.measurement.zzgb$zza: com.google.android.gms.internal.measurement.zzkc zze
androidx.datastore.preferences.PreferencesProto$Value: int INTEGER_FIELD_NUMBER
io.flutter.embedding.engine.FlutterJNI: float refreshRateFPS
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean ignoringFence
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified firebaseApp
com.google.android.gms.internal.measurement.zzfh$zzb: com.google.android.gms.internal.measurement.zzfh$zzb zzc
com.google.android.gms.internal.measurement.zzft$zzj: long zzw
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggeredTimestamp
com.google.android.gms.internal.measurement.zzgb$zzb: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzj: boolean zzbh
com.google.android.gms.internal.measurement.zzft$zzg: int zze
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzft$zzk zzas
com.google.android.gms.internal.measurement.zzfn$zzb: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzfh$zzf: int zze
androidx.datastore.preferences.PreferencesProto$StringSet: int STRINGS_FIELD_NUMBER
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean needsSave
com.google.android.gms.internal.measurement.zzft$zzg: double zzj
io.flutter.view.FlutterCallbackInformation: java.lang.String callbackName
com.google.android.gms.internal.measurement.zzfn$zze: int zzh
com.google.android.gms.internal.measurement.zzfh$zzf: boolean zzh
com.google.android.gms.internal.measurement.zzft$zzg: float zzi
io.flutter.embedding.engine.FlutterJNI: float displayHeight
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTriggerTimeout
com.google.android.gms.internal.measurement.zzft$zzb: int zze
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean newFrameAvailable
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzba
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean VERBOSE_LOGS
com.google.android.gms.internal.measurement.zzft$zzb: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzkc zzh
androidx.datastore.preferences.PreferencesProto$Value: int valueCase_
com.google.android.gms.internal.measurement.zzfn$zzh: java.lang.String zzg
com.google.android.gms.measurement.internal.zzal: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzfh$zzb: java.lang.String zzg
io.flutter.embedding.engine.FlutterJNI: java.lang.String TAG
io.flutter.view.AccessibilityViewEmbedder: android.util.SparseArray flutterIdToOrigin
com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty: long mTimeToLive
com.google.android.gms.internal.measurement.zzfn$zzh: int zzi
com.google.android.gms.internal.measurement.zzfh$zzd: java.lang.String zzi
com.google.android.gms.measurement.internal.zzbf: android.os.Parcelable$Creator CREATOR
com.google.android.gms.internal.measurement.zzft$zzf: com.google.android.gms.internal.measurement.zzft$zzf zzc
com.google.android.gms.internal.measurement.zzft$zzi: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zzf: int zze
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean released
kotlinx.coroutines.JobSupport$Finishing: int _isCompleting
com.google.android.gms.internal.measurement.zzft$zzj: long zzbg
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean trimOnMemoryPressure
com.google.android.gms.internal.measurement.zzfn$zza$zzf: com.google.android.gms.internal.measurement.zzlp zzd
androidx.datastore.preferences.PreferencesProto$PreferenceMap: androidx.datastore.preferences.protobuf.Parser PARSER
androidx.datastore.preferences.protobuf.GeneratedMessageLite: java.util.Map defaultInstanceMap
com.google.android.gms.internal.measurement.zzfh$zzf: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzfn$zze: int zzg
com.google.android.gms.internal.measurement.zzft$zzj: long zzm
com.google.android.gms.internal.measurement.zzfn$zza$zzc: com.google.android.gms.internal.measurement.zzlp zzd
com.google.android.gms.internal.measurement.zzgb$zza: com.google.android.gms.internal.measurement.zzlp zzd
io.flutter.embedding.engine.FlutterJNI: java.util.Set engineLifecycleListeners
kotlinx.coroutines.internal.Segment: int cleanedAndPointers
androidx.transition.ChangeBounds$7: androidx.transition.ChangeBounds$ViewBounds mViewBounds
com.google.android.gms.internal.measurement.zzfn$zzd: long zzf
com.google.android.gms.internal.measurement.zzft$zzn: long zzi
com.google.android.gms.internal.measurement.zzft$zzj: int zzf
com.google.android.gms.internal.measurement.zzft$zzi: com.google.android.gms.internal.measurement.zzft$zzi zzc
io.flutter.view.AccessibilityViewEmbedder: io.flutter.view.AccessibilityViewEmbedder$ReflectionAccessors reflectionAccessors
com.google.android.gms.internal.measurement.zzfn$zzg: java.lang.String zzg
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: androidx.lifecycle.ProcessLifecycleOwner this$0
androidx.datastore.preferences.PreferencesProto$Value: androidx.datastore.preferences.PreferencesProto$Value DEFAULT_INSTANCE
com.google.android.gms.internal.measurement.zzgb$zzc: com.google.android.gms.internal.measurement.zzgb$zza zzg
com.google.android.gms.internal.measurement.zzft$zzd: long zzg
kotlinx.coroutines.channels.BufferedChannel: java.lang.Object _closeCause
androidx.datastore.preferences.protobuf.GeneratedMessageLite: int MUTABLE_FLAG_MASK
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzal
com.google.android.gms.internal.measurement.zzft$zzj: int zze
kotlinx.coroutines.JobSupport$Finishing: java.lang.Object _exceptionsHolder
com.google.android.gms.internal.measurement.zzft$zze: long zzi
com.google.android.gms.internal.measurement.zzgb$zzb: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzfn$zza$zzb: int zzf
com.google.android.gms.internal.measurement.zzft$zzj: java.lang.String zzv
kotlinx.coroutines.CancellableContinuationImpl: java.lang.Object _parentHandle
com.google.android.gms.internal.measurement.zzft$zzj: com.google.android.gms.internal.measurement.zzka zzat
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: java.lang.ClassLoader sClassLoader
io.flutter.embedding.engine.FlutterJNI: io.flutter.plugin.platform.PlatformViewsController2 platformViewsController2
kotlinx.coroutines.channels.BufferedChannel: long completedExpandBuffersAndPauseFlag
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.components.Qualified sessionsSettings
com.google.android.gms.internal.measurement.zzft$zzl: com.google.android.gms.internal.measurement.zzkc zzg
com.google.android.gms.internal.measurement.zzft$zzb: boolean zzj
com.google.android.gms.internal.measurement.zzft$zzj: long zzn
androidx.lifecycle.ProcessLifecycleOwner$attach$1: androidx.lifecycle.ProcessLifecycleOwner this$0
kotlinx.coroutines.internal.LockFreeLinkedListNode: java.lang.Object _next
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader lastReaderDequeuedFrom
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetTop(android.view.DisplayCutout)
androidx.appcompat.widget.ActionBarOverlayLayout: void setShowingForActionMode(boolean)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.appcompat.widget.AppCompatCheckBox: int getCompoundPaddingLeft()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: FlutterFirebasePluginRegistry()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: int access$200(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.view.TextureRegistry$SurfaceProducer: void release()
io.flutter.plugins.firebase.analytics.FlutterFirebaseAnalyticsPlugin: FlutterFirebaseAnalyticsPlugin()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifierBase(int)
androidx.activity.OnBackPressedDispatcher$Api33Impl: void registerOnBackInvokedCallback(java.lang.Object,int,java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateRefreshRate(float)
kotlinx.coroutines.android.AndroidDispatcherFactory: int getLoadPriority()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowTitle(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$ItemAnimator getItemAnimator()
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setSize(int,int)
androidx.recyclerview.widget.RecyclerView: void setAdapter(androidx.recyclerview.widget.RecyclerView$Adapter)
com.google.android.gms.internal.measurement.zzft$zzi$zza: com.google.android.gms.internal.measurement.zzft$zzi$zza[] values()
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag[] values()
com.google.android.datatransport.runtime.backends.TransportBackendDiscovery: TransportBackendDiscovery()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMaximumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getTappableElementInsets()
androidx.core.view.WindowInsetsCompat$Impl28: int hashCode()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: int getRootAlpha()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void release()
androidx.recyclerview.widget.RecyclerView: int getMaxFlingVelocity()
androidx.core.view.ViewCompat$Api30Impl: int getImportantForContentCapture(android.view.View)
com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus: com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: java.lang.String[] computePlatformResolvedLocale(java.lang.String[])
com.google.firebase.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStopped(android.app.Activity)
androidx.core.graphics.drawable.IconCompat$Api30Impl: android.graphics.drawable.Icon createWithAdaptiveBitmapContentUri(android.net.Uri)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getWidth()
androidx.core.view.ViewConfigurationCompat$Api28Impl: int getScaledHoverSlop(android.view.ViewConfiguration)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCheckMarkDrawable(int)
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType[] values()
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOpNoThrow(android.app.AppOpsManager,java.lang.String,java.lang.String)
androidx.appcompat.widget.ActionBarContainer: void setStackedBackground(android.graphics.drawable.Drawable)
dev.fluttercommunity.plus.share.ShareFileProvider: ShareFileProvider()
androidx.recyclerview.widget.RecyclerView: void setRecycledViewPool(androidx.recyclerview.widget.RecyclerView$RecycledViewPool)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsetsForType(int,boolean)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int getHeight()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotX(float)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(java.lang.CharSequence)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setNumericShortcut(android.view.MenuItem,char,int)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetEnd()
com.google.firebase.analytics.FirebaseAnalytics: com.google.android.gms.measurement.internal.zzkn getScionFrontendApiImplementation(android.content.Context,android.os.Bundle)
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.google.firebase.installations.ktx.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
androidx.core.view.ViewCompat$Api26Impl: boolean isImportantForAutofill(android.view.View)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getUserProperties(java.lang.String,java.lang.String,boolean,com.google.android.gms.internal.measurement.zzdi)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathEnd(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setAlpha(float)
com.google.firebase.concurrent.ExecutorsRegistrar: ExecutorsRegistrar()
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: void setOnSuggestionListener(androidx.appcompat.widget.SearchView$OnSuggestionListener)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointVariantSelector(int)
androidx.preference.SwitchPreference: SwitchPreference(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.net.Uri getUri(java.lang.Object)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundResource(int)
androidx.appcompat.widget.Toolbar: void setSubtitle(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(int)
androidx.appcompat.widget.AppCompatImageButton: void setImageBitmap(android.graphics.Bitmap)
androidx.appcompat.widget.AppCompatEditText: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityResumed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader29()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl29)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl: android.hardware.biometrics.BiometricPrompt$AuthenticationCallback createCallback(androidx.biometric.AuthenticationCallbackProvider$Listener)
io.flutter.plugins.sharedpreferences.SharedPreferencesPlugin: SharedPreferencesPlugin()
androidx.appcompat.widget.AppCompatEditText: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initForTests(java.util.Map)
androidx.core.content.ContextCompat$Api23Impl: java.lang.String getSystemServiceName(android.content.Context,java.lang.Class)
com.google.android.gms.internal.measurement.zzjo: com.google.android.gms.internal.measurement.zzjo[] values()
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface()
androidx.appcompat.widget.AppCompatEditText: java.lang.CharSequence getText()
androidx.core.view.ViewCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void detachFromNativeAndReleaseResources()
androidx.appcompat.widget.SwitchCompat: boolean getTargetCheckedState()
androidx.preference.PreferenceCategory: PreferenceCategory(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityHeading(android.view.View,boolean)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationError(int,java.lang.CharSequence)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getFinalClippingPaths()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsetsAnimation$Callback getAnimationCallback()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathStart(float)
com.google.android.gms.internal.measurement.zzs: com.google.android.gms.internal.measurement.zzs[] values()
com.google.android.datatransport.cct.CctBackendFactory: CctBackendFactory()
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabled(boolean)
io.flutter.view.TextureRegistry$ImageConsumer: android.media.Image acquireLatestImage()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: FlutterRenderer$ImageReaderSurfaceProducer(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
com.google.firebase.FirebaseCommonKtxRegistrar: java.util.List getComponents()
androidx.core.content.ContextCompat$Api24Impl: boolean isDeviceProtectedStorage(android.content.Context)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setTappableElementInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatImageView: void setImageURI(android.net.Uri)
androidx.preference.Preference: Preference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: void setOnQueryTextListener(androidx.appcompat.widget.SearchView$OnQueryTextListener)
androidx.core.view.WindowInsetsCompat$Impl21: boolean isConsumed()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setStableInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceProducer: long id()
androidx.appcompat.widget.AppCompatTextView: void setBackgroundResource(int)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getNavigationIcon()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewCompat$Api29Impl: void setSystemGestureExclusionRects(android.view.View,java.util.List)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: FlutterMutatorsStack()
androidx.core.view.WindowInsetsCompat$Impl20: boolean isRound()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getFillColor()
io.flutter.embedding.engine.FlutterJNI: boolean getIsSoftwareRenderingEnabled()
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getLogo()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay valueOf(java.lang.String)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: void onActivityCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void asyncWaitForVsync(long)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
com.google.firebase.analytics.FirebaseAnalytics$ConsentType: com.google.firebase.analytics.FirebaseAnalytics$ConsentType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void cleanup()
androidx.appcompat.widget.SwitchCompat: void setTrackDrawable(android.graphics.drawable.Drawable)
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.String permissionToOp(java.lang.String)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29(androidx.core.view.WindowInsetsCompat)
androidx.core.widget.PopupWindowCompat$Api23Impl: boolean getOverlapAnchor(android.widget.PopupWindow)
androidx.recyclerview.widget.RecyclerView: void setRecyclerListener(androidx.recyclerview.widget.RecyclerView$RecyclerListener)
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getType(java.lang.Object)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getLogoDescription()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void finalize()
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurfaces()
com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior: com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior[] values()
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidExceptionPreHandler: void handleException(kotlin.coroutines.CoroutineContext,java.lang.Throwable)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType[] values()
io.flutter.embedding.engine.FlutterJNI: void showOverlaySurface2()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateX(float)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getConditionalUserProperties(java.lang.String,java.lang.String,com.google.android.gms.internal.measurement.zzdi)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$Adapter getAdapter()
androidx.recyclerview.widget.RecyclerView: void setAccessibilityDelegateCompat(androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate)
com.google.android.gms.internal.measurement.zzbv: com.google.android.gms.internal.measurement.zzbv[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPreCreated(android.app.Activity,android.os.Bundle)
io.flutter.embedding.android.FlutterView: void setWindowInfoListenerDisplayFeatures(androidx.window.layout.WindowLayoutInfo)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetRight()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScroll(android.view.ViewParent,android.view.View,int,int,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleX()
androidx.appcompat.widget.AppCompatSpinner: androidx.appcompat.widget.AppCompatSpinner$SpinnerPopup getInternalPopup()
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type[] values()
androidx.core.widget.TextViewCompat$Api23Impl: android.content.res.ColorStateList getCompoundDrawableTintList(android.widget.TextView)
androidx.recyclerview.widget.RecyclerView: void setLayoutManager(androidx.recyclerview.widget.RecyclerView$LayoutManager)
androidx.appcompat.view.menu.ActionMenuItemView: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action,java.lang.Object)
androidx.datastore.preferences.protobuf.WireFormat$FieldType: androidx.datastore.preferences.protobuf.WireFormat$FieldType valueOf(java.lang.String)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetLeft(android.view.DisplayCutout)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
io.flutter.embedding.engine.FlutterJNI: void removeEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void isDataCollectionEnabled(com.google.android.gms.internal.measurement.zzdi)
io.flutter.embedding.engine.FlutterJNI: void endFrame2()
androidx.appcompat.widget.ViewStubCompat: void setVisibility(int)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQuery()
androidx.appcompat.widget.ActionBarContainer: void setTabContainer(androidx.appcompat.widget.ScrollingTabContainerView)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getTitle()
androidx.core.content.FileProvider$Api21Impl: java.io.File[] getExternalMediaDirs(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
io.flutter.embedding.android.FlutterImageView: android.view.Surface getSurface()
androidx.appcompat.widget.ButtonBarLayout: int getMinimumHeight()
com.google.firebase.sessions.EventType: com.google.firebase.sessions.EventType[] values()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onEnd(android.view.WindowInsetsAnimation)
androidx.appcompat.widget.SwitchCompat: void setShowText(boolean)
androidx.appcompat.widget.DropDownListView: void setListSelectionHidden(boolean)
androidx.appcompat.widget.ActionBarContainer: android.view.View getTabContainer()
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void detachFromGLContext()
androidx.appcompat.view.menu.ListMenuItemView: void setTitle(java.lang.CharSequence)
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: FirebaseCommonKtxRegistrar()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostResumed(android.app.Activity)
com.google.firebase.sessions.LogEnvironment: com.google.firebase.sessions.LogEnvironment valueOf(java.lang.String)
androidx.core.content.res.FontResourcesParserCompat$Api21Impl: int getType(android.content.res.TypedArray,int)
androidx.core.content.ContextCompat$Api21Impl: java.io.File getCodeCacheDir(android.content.Context)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportButtonTintList()
androidx.recyclerview.widget.RecyclerView: void setClipToPadding(boolean)
androidx.preference.DialogPreference: DialogPreference(android.content.Context,android.util.AttributeSet)
androidx.recyclerview.widget.RecyclerView: void setViewCacheExtension(androidx.recyclerview.widget.RecyclerView$ViewCacheExtension)
androidx.core.view.ViewGroupCompat$Api21Impl: void setTransitionGroup(android.view.ViewGroup,boolean)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeTextType()
androidx.appcompat.widget.AppCompatEditText: android.text.Editable getText()
androidx.core.view.WindowInsetsCompat$Impl21: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatSpinner: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.widget.EdgeEffectCompat$Api31Impl: android.widget.EdgeEffect create(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.plugins.GeneratedPluginRegistrant: GeneratedPluginRegistrant()
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: int getContentInsetStart()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
com.google.android.gms.measurement.AppMeasurement: void beginAdUnitExposure(java.lang.String)
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: FirebaseCrashlyticsKtxRegistrar()
androidx.core.content.res.ResourcesCompat$Api23Impl: int getColor(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getStableInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeLoadDartDeferredLibrary(long,int,java.lang.String[])
androidx.biometric.BiometricFragment$Api28Impl: void setTitle(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityDestroyed(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.widget.NestedScrollView: void setFillViewport(boolean)
kotlin.collections.AbstractList: AbstractList()
androidx.biometric.CancellationSignalProvider$Api16Impl: void cancel(android.os.CancellationSignal)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView$SearchAutoComplete: int getSearchViewTextMinWidthDp()
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow[] values()
androidx.core.widget.NestedScrollView: float getBottomFadingEdgeStrength()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSessionTimeoutDuration(long)
com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar: java.util.List getComponents()
androidx.lifecycle.ProcessLifecycleOwner$Api29Impl: void registerActivityLifecycleCallbacks(android.app.Activity,android.app.Application$ActivityLifecycleCallbacks)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType[] values()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.view.ViewCompat$Api26Impl: boolean restoreDefaultFocus(android.view.View)
io.flutter.embedding.engine.FlutterJNI: boolean nativeGetIsSoftwareRenderingEnabled()
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: FirebaseCommonLegacyRegistrar()
androidx.appcompat.widget.ActionBarOverlayLayout: int getNestedScrollAxes()
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax[] values()
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation valueOf(java.lang.String)
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedFling(android.view.View,float,float,boolean)
com.google.android.gms.measurement.AppMeasurement: void endAdUnitExposure(java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo getRootNode(android.view.View,int,android.graphics.Rect)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState[] values()
androidx.core.content.ContextCompat$Api26Impl: android.content.ComponentName startForegroundService(android.content.Context,android.content.Intent)
androidx.preference.PreferenceScreen: PreferenceScreen(android.content.Context,android.util.AttributeSet)
androidx.biometric.BiometricFragment$Api28Impl: void setSubtitle(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
androidx.biometric.CryptoObjectUtils$Api30Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(android.security.identity.IdentityCredential)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setPivotY(float)
com.google.firebase.sessions.LogEnvironment: com.google.firebase.sessions.LogEnvironment[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: void setStateDescription(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnTrimMemoryListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.appcompat.widget.SearchView: int getImeOptions()
androidx.appcompat.view.menu.ActionMenuItemView: void setExpandedFormat(boolean)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State[] values()
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityStopped(android.app.Activity)
androidx.biometric.BiometricManager$Api29Impl: int canAuthenticate(android.hardware.biometrics.BiometricManager)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$LayoutManager getLayoutManager()
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(android.content.res.ColorStateList)
androidx.fragment.app.DefaultSpecialEffectsController$Api24Impl: long totalDuration(android.animation.AnimatorSet)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeStepGranularity()
androidx.appcompat.widget.AppCompatImageButton: void setImageResource(int)
io.flutter.plugins.urllauncher.WebViewActivity: WebViewActivity()
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView2(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void ensureNotAttachedToNative()
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.res.Resources,int,android.content.res.Resources$Theme)
androidx.appcompat.widget.SwitchCompat: boolean getShowText()
androidx.appcompat.widget.ActionBarContextView: void setTitleOptional(boolean)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: android.graphics.Matrix getLocalMatrix()
androidx.appcompat.widget.AppCompatSpinner: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: void copyRootViewBounds(android.view.View)
io.flutter.plugin.platform.PlatformViewWrapper: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.appcompat.widget.ActionBarOverlayLayout: void setUiOptions(int)
androidx.appcompat.widget.SwitchCompat: boolean getSplitTrack()
androidx.core.view.ViewCompat$Api21Impl$1: ViewCompat$Api21Impl$1(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
androidx.appcompat.widget.ActionBarContainer: void setPrimaryBackground(android.graphics.drawable.Drawable)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreStopped(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void pushImage(android.media.Image)
com.google.firebase.sessions.EventType: com.google.firebase.sessions.EventType valueOf(java.lang.String)
androidx.appcompat.view.menu.ListMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.AppCompatTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.content.ContextCompat$Api24Impl: android.content.Context createDeviceProtectedStorageContext(android.content.Context)
androidx.fragment.app.FragmentContainerView: void setDrawDisappearingViewsLast(boolean)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: java.io.FileDescriptor dup(java.io.FileDescriptor)
androidx.recyclerview.widget.RecyclerView: void setItemViewCacheSize(int)
com.google.android.gms.measurement.AppMeasurement: void setConditionalUserProperty(com.google.android.gms.measurement.AppMeasurement$ConditionalUserProperty)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: boolean isHardwareDetected(java.lang.Object)
androidx.transition.FragmentTransitionSupport: FragmentTransitionSupport()
androidx.appcompat.widget.Toolbar: int getContentInsetRight()
androidx.recyclerview.widget.RecyclerView: int getBaseline()
androidx.lifecycle.LegacySavedStateHandleController$OnRecreation: LegacySavedStateHandleController$OnRecreation()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat$CollectionItemInfoCompat buildCollectionItemInfoCompat(boolean,int,int,int,int,boolean,java.lang.String,java.lang.String)
kotlinx.coroutines.channels.BufferOverflow: kotlinx.coroutines.channels.BufferOverflow valueOf(java.lang.String)
androidx.core.content.ContextCompat$Api26Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStopped(android.app.Activity)
com.google.firebase.crashlytics.internal.common.DeliveryMechanism: com.google.firebase.crashlytics.internal.common.DeliveryMechanism valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState[] values()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebSourceAsync(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsVariationSelector(int)
androidx.core.view.ViewCompat$Api28Impl: void removeOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
androidx.core.graphics.drawable.IconCompat$Api28Impl: java.lang.String getResPackage(java.lang.Object)
io.flutter.plugins.localauth.Messages$AuthResult: io.flutter.plugins.localauth.Messages$AuthResult[] values()
androidx.appcompat.widget.AppCompatEditText: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingLeft()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getRotation()
io.flutter.plugins.imagepicker.ImagePickerPlugin: ImagePickerPlugin()
androidx.appcompat.widget.SwitchCompat: void setTrackResource(int)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object deleteRegistrations(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest,kotlin.coroutines.Continuation)
androidx.appcompat.widget.Toolbar: void setTitleMarginStart(int)
androidx.appcompat.widget.LinearLayoutCompat: int getOrientation()
androidx.appcompat.widget.SearchView: void setIconified(boolean)
androidx.appcompat.widget.SwitchCompat: int getThumbScrollRange()
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setSystemWindowInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void loadLibrary(android.content.Context)
androidx.core.view.ViewCompat$Api26Impl: void setImportantForAutofill(android.view.View,int)
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.graphics.PorterDuff$Mode getButtonTintMode(android.widget.CompoundButton)
androidx.browser.browseractions.BrowserActionsFallbackMenuView: BrowserActionsFallbackMenuView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: void setMaxWidth(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchPointerDataPacket(java.nio.ByteBuffer,int)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int,int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeStableInsets()
io.flutter.embedding.engine.FlutterJNI: void hideOverlaySurface2()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void performAction(android.os.Bundle,com.google.android.gms.internal.measurement.zzdi,long)
androidx.appcompat.widget.SwitchCompat: void setThumbTextPadding(int)
androidx.appcompat.widget.LinearLayoutCompat: int getShowDividers()
androidx.core.app.ActivityCompat$Api23Impl: void requestPermissions(android.app.Activity,java.lang.String[],int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContainer: ActionBarContainer(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetLeft()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerTrigger(android.net.Uri,kotlin.coroutines.Continuation)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatButton: void setAutoSizeTextTypeWithDefaults(int)
com.google.android.gms.internal.measurement.zznq: com.google.android.gms.internal.measurement.zznq[] values()
com.google.android.gms.measurement.AppMeasurement: void logEventInternal(java.lang.String,java.lang.String,android.os.Bundle)
androidx.appcompat.widget.ActionMenuView: int getPopupTheme()
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void setCurrentPlayTime(android.animation.AnimatorSet,long)
androidx.recyclerview.widget.LinearLayoutManager: LinearLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.Toolbar: android.view.MenuInflater getMenuInflater()
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Icon toIcon(androidx.core.graphics.drawable.IconCompat,android.content.Context)
androidx.core.view.ViewCompat$Api30Impl: void setStateDescription(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.LinearLayoutCompat: float getWeightSum()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setWindowLayoutType(android.widget.PopupWindow,int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,int,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void setRefreshRateFPS(float)
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessage(java.lang.String,java.nio.ByteBuffer,int,long)
androidx.appcompat.widget.SearchView: int getMaxWidth()
io.flutter.embedding.engine.FlutterJNI: void nativeScheduleFrame(long)
com.google.android.gms.dynamite.descriptors.com.google.android.gms.measurement.dynamite.ModuleDescriptor: ModuleDescriptor()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type[] values()
io.flutter.embedding.engine.FlutterJNI: FlutterJNI()
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.PlatformView getView()
io.flutter.embedding.engine.FlutterJNI: void nativeNotifyLowMemoryWarning(long)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int)
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart[] values()
androidx.appcompat.widget.AppCompatTextView: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.appcompat.view.menu.ActionMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.profileinstaller.ProfileInstallerInitializer: ProfileInstallerInitializer()
androidx.appcompat.widget.SearchView: void setImeOptions(int)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: void setNavigationIcon(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleY(float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setScaleX(float)
io.flutter.embedding.engine.FlutterJNI: void attachToNative()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void install()
io.flutter.embedding.engine.FlutterJNI: void dispatchEmptyPlatformMessage(java.lang.String,int)
androidx.core.view.ViewCompat$Api21Impl: boolean isNestedScrollingEnabled(android.view.View)
androidx.appcompat.widget.Toolbar: android.widget.TextView getSubtitleTextView()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void releaseInternal()
androidx.appcompat.widget.LinearLayoutCompat: void setShowDividers(int)
androidx.core.view.ViewCompat$Api21Impl: android.content.res.ColorStateList getBackgroundTintList(android.view.View)
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: int getLayoutDirection(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintMode(android.widget.CompoundButton,android.graphics.PorterDuff$Mode)
androidx.profileinstaller.ProfileInstallerInitializer$Choreographer16Impl: void postFrameCallback(java.lang.Runnable)
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMaxTextSize()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$300(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader access$700(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.ActivityChooserView$InnerLayout: ActivityChooserView$InnerLayout(android.content.Context,android.util.AttributeSet)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConditionalUserProperty(android.os.Bundle,long)
io.flutter.embedding.engine.FlutterJNI: void loadDartDeferredLibrary(int,java.lang.String[])
io.flutter.plugins.imagepicker.ImagePickerFileProvider: ImagePickerFileProvider()
androidx.appcompat.widget.SearchView: void setOnQueryTextFocusChangeListener(android.view.View$OnFocusChangeListener)
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(javax.crypto.Mac)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setTranslateY(float)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons[] values()
androidx.appcompat.widget.SwitchCompat: void setThumbDrawable(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getFillAlpha()
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifier(int)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.settings.SessionsSettings getComponents$lambda-3(com.google.firebase.components.ComponentContainer)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterJNI: boolean IsSurfaceControlEnabled()
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: ImeSyncDeferringInsetsCallback(android.view.View)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAlignedChildIndex(int)
androidx.appcompat.widget.AppCompatSpinner: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: FlutterFirebaseCoreRegistrar()
androidx.core.widget.TextViewCompat$Api24Impl: android.icu.text.DecimalFormatSymbols getInstance(java.util.Locale)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImage dequeueImage()
androidx.appcompat.widget.ViewStubCompat: void setLayoutInflater(android.view.LayoutInflater)
androidx.core.view.ViewCompat$Api21Impl: float getElevation(android.view.View)
com.google.firebase.ktx.FirebaseCommonLegacyRegistrar: java.util.List getComponents()
androidx.core.view.WindowInsetsCompat$Impl29: WindowInsetsCompat$Impl29(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.biometric.BiometricFragment$Api28Impl: android.hardware.biometrics.BiometricPrompt$Builder createPromptBuilder(android.content.Context)
androidx.browser.customtabs.CustomTabsIntent$Api24Impl: java.lang.String getDefaultLocale()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void beginAdUnitExposure(java.lang.String,long)
com.google.firebase.FirebaseCommonRegistrar: FirebaseCommonRegistrar()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void removeRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
com.google.android.gms.measurement.internal.zzir: com.google.android.gms.measurement.internal.zzir[] values()
androidx.appcompat.widget.ViewStubCompat: ViewStubCompat(android.content.Context,android.util.AttributeSet)
androidx.biometric.BiometricManager$Api29Impl: android.hardware.biometrics.BiometricManager create(android.content.Context)
androidx.core.view.MenuItemCompat$Api26Impl: int getAlphabeticModifiers(android.view.MenuItem)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetBottom(android.view.DisplayCutout)
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: void onActivityPostResumed(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: long id()
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetWidth()
androidx.fragment.app.DefaultSpecialEffectsController$Api26Impl: void reverse(android.animation.AnimatorSet)
androidx.appcompat.widget.AppCompatEditText: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: AppCompatTextViewAutoSizeHelper$Impl23()
io.flutter.plugins.imagepicker.Messages$CacheRetrievalType: io.flutter.plugins.imagepicker.Messages$CacheRetrievalType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void waitOnFence(android.media.Image)
androidx.appcompat.widget.AppCompatCheckBox: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintMode(android.widget.ImageView,android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void release()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
com.google.firebase.provider.FirebaseInitProvider: FirebaseInitProvider()
androidx.appcompat.widget.ActionMenuView: void setPopupTheme(int)
com.google.android.gms.internal.measurement.zzlr: com.google.android.gms.internal.measurement.zzlr[] values()
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationFailed()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$402(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.View)
androidx.browser.customtabs.CustomTabsIntent$Api23Impl: android.app.ActivityOptions makeBasicActivityOptions()
kotlinx.coroutines.android.AndroidDispatcherFactory: AndroidDispatcherFactory()
androidx.appcompat.widget.AppCompatTextView: void setLineHeight(int)
androidx.core.app.RemoteActionCompat: RemoteActionCompat()
androidx.preference.SeekBarPreference: SeekBarPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionMenuView: void setOnMenuItemClickListener(androidx.appcompat.widget.ActionMenuView$OnMenuItemClickListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeWidth()
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onImage(android.media.ImageReader,android.media.Image)
androidx.appcompat.widget.SearchView: void setQuery(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatEditText: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
kotlin.coroutines.intrinsics.CoroutineSingletons: kotlin.coroutines.intrinsics.CoroutineSingletons valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void runBundleAndSnapshotFromLibrary(java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
androidx.appcompat.widget.Toolbar: int getCurrentContentInsetStart()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeColor(int)
io.flutter.embedding.engine.FlutterJNI: java.lang.String getVMServiceUri()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setQueryFromAppProcessEnabled(android.view.accessibility.AccessibilityNodeInfo,android.view.View,boolean)
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType[] values()
androidx.appcompat.widget.ActionMenuView: android.view.Menu getMenu()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap nativeGetBitmap(long)
androidx.appcompat.widget.ActionBarOverlayLayout: void setIcon(android.graphics.drawable.Drawable)
androidx.preference.EditTextPreference: EditTextPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo,long)
androidx.core.os.BundleApi21ImplKt: void putSize(android.os.Bundle,java.lang.String,android.util.Size)
androidx.appcompat.widget.ActionBarOverlayLayout: int getActionBarHideOffset()
com.google.android.gms.measurement.AppMeasurement: java.util.Map getUserProperties(java.lang.String,java.lang.String,boolean)
io.flutter.embedding.engine.FlutterJNI: void updateRefreshRate()
io.flutter.plugin.platform.PlatformViewWrapper: android.view.ViewTreeObserver$OnGlobalFocusChangeListener getActiveFocusListener()
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.DecorToolbar getWrapper()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void onTrimMemory(int)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMinor()
io.flutter.embedding.android.FlutterView: void setVisibility(int)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State[] values()
com.google.android.gms.measurement.AppMeasurement: java.lang.String getAppInstanceId()
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.FlutterJNI: long performNativeAttach(io.flutter.embedding.engine.FlutterJNI)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void disableFenceForTest()
androidx.appcompat.widget.ActionBarContextView: void setVisibility(int)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: com.google.firebase.analytics.connector.AnalyticsConnector lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
io.flutter.embedding.engine.FlutterJNI: boolean isAttached()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$getPluginConstantsForFirebaseApp$0(com.google.firebase.FirebaseApp,com.google.android.gms.tasks.TaskCompletionSource)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: androidx.core.view.WindowInsetsCompat build()
androidx.recyclerview.widget.RecyclerView: void setNestedScrollingEnabled(boolean)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCachedAppInstanceId(com.google.android.gms.internal.measurement.zzdi)
io.flutter.embedding.engine.FlutterJNI: void swapTransactions()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedWidthMajor()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarHideOffset(int)
com.google.common.collect.Ordering: Ordering()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getUniqueId(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.view.TextureRegistry$SurfaceProducer: int getWidth()
androidx.appcompat.widget.Toolbar: void setCollapseIcon(int)
androidx.core.app.ActivityCompat$Api32Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
io.flutter.plugins.firebase.crashlytics.FirebaseCrashlyticsTestCrash: FirebaseCrashlyticsTestCrash()
androidx.core.app.NotificationManagerCompat$Api24Impl: boolean areNotificationsEnabled(android.app.NotificationManager)
androidx.appcompat.widget.Toolbar: void setCollapsible(boolean)
androidx.core.view.ViewCompat$Api21Impl: void setElevation(android.view.View,float)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.PlatformView,io.flutter.plugin.platform.AccessibilityEventsDelegate,int,android.view.View$OnFocusChangeListener)
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType[] values()
androidx.preference.internal.PreferenceImageView: void setMaxHeight(int)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchSemanticsAction(long,int,int,java.nio.ByteBuffer,int)
androidx.core.view.ViewCompat$Api23Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
com.google.android.gms.internal.measurement.zzfn$zza$zzd: com.google.android.gms.internal.measurement.zzfn$zza$zzd[] values()
io.flutter.view.AccessibilityBridge$StringAttributeType: io.flutter.view.AccessibilityBridge$StringAttributeType valueOf(java.lang.String)
androidx.core.widget.NestedScrollView: int getNestedScrollAxes()
androidx.biometric.BiometricFragment$Api28Impl: void authenticate(android.hardware.biometrics.BiometricPrompt,android.hardware.biometrics.BiometricPrompt$CryptoObject,android.os.CancellationSignal,java.util.concurrent.Executor,android.hardware.biometrics.BiometricPrompt$AuthenticationCallback)
io.flutter.embedding.android.FlutterView: void setDelegate(io.flutter.embedding.android.FlutterViewDelegate)
androidx.lifecycle.ReportFragment: ReportFragment()
com.google.firebase.datatransport.TransportRegistrar: com.google.android.datatransport.TransportFactory lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void applyInsetTypes()
androidx.appcompat.widget.SearchView: void setInputType(int)
androidx.core.view.ViewCompat$Api26Impl: boolean hasExplicitFocusable(android.view.View)
androidx.appcompat.widget.AppCompatRadioButton: void setButtonDrawable(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setUniqueId(android.view.accessibility.AccessibilityNodeInfo,java.lang.String)
androidx.biometric.CryptoObjectUtils$Api28Impl: javax.crypto.Mac getMac(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setTrimPathOffset(float)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenName()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageResponseCallback(long,int,java.nio.ByteBuffer,int)
com.google.firebase.sessions.FirebaseSessionsRegistrar: FirebaseSessionsRegistrar()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void addRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.ActionBarContainer: void setSplitBackground(android.graphics.drawable.Drawable)
androidx.fragment.app.FragmentContainerView: void setLayoutTransition(android.animation.LayoutTransition)
androidx.fragment.app.SpecialEffectsController$Operation$State: androidx.fragment.app.SpecialEffectsController$Operation$State valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: android.content.res.ColorStateList getIconTintList(android.view.MenuItem)
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType valueOf(java.lang.String)
io.flutter.plugins.pathprovider.PathProviderPlugin: PathProviderPlugin()
androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact: androidx.fragment.app.SpecialEffectsController$Operation$LifecycleImpact[] values()
androidx.appcompat.widget.FitWindowsLinearLayout: FitWindowsLinearLayout(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipPath(android.graphics.Path)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void registerOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzdj)
androidx.core.view.ViewParentCompat$Api21Impl: boolean onStartNestedScroll(android.view.ViewParent,android.view.View,android.view.View,int)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo convertToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,int,android.view.View)
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.android.FlutterImageView getCurrentImageSurface()
io.flutter.embedding.engine.FlutterJNI: void dispatchPlatformMessage(java.lang.String,java.nio.ByteBuffer,int,int)
androidx.appcompat.widget.SwitchCompat: void setChecked(boolean)
androidx.core.view.ViewCompat$Api30Impl: void setImportantForContentCapture(android.view.View,int)
androidx.core.view.ViewCompat$Api21Impl: void setZ(android.view.View,float)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: android.view.accessibility.AccessibilityNodeInfo$ExtraRenderingInfo getExtraRenderingInfo(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.view.menu.ActionMenuItemView: void setCheckable(boolean)
kotlinx.coroutines.android.AndroidDispatcherFactory: java.lang.String hintOnError()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.core.view.WindowInsetsCompat$Impl30: void copyRootViewBounds(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceWindowChanged(long,android.view.Surface)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager$CryptoObject wrapCryptoObject(androidx.core.hardware.fingerprint.FingerprintManagerCompat$CryptoObject)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: androidx.core.hardware.fingerprint.FingerprintManagerCompat$CryptoObject unwrapCryptoObject(java.lang.Object)
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: long lseek(java.io.FileDescriptor,long,int)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl30)
androidx.core.content.ContextCompat$Api21Impl: android.graphics.drawable.Drawable getDrawable(android.content.Context,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStopped(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.recyclerview.widget.RecyclerView: RecyclerView(android.content.Context,android.util.AttributeSet)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean hasRequestInitialAccessibilityFocus(android.view.accessibility.AccessibilityNodeInfo)
androidx.window.extensions.core.util.function.Consumer: void accept(java.lang.Object)
io.flutter.plugins.urllauncher.UrlLauncherPlugin: UrlLauncherPlugin()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getGmpAppId(com.google.android.gms.internal.measurement.zzdi)
androidx.core.view.ViewCompat$Api21Impl: float getZ(android.view.View)
androidx.appcompat.widget.Toolbar: void setContentInsetStartWithNavigation(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotY()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRect(int,int,int,int)
androidx.appcompat.widget.LinearLayoutCompat: int getBaselineAlignedChildIndex()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityStarted(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.biometric.CancellationSignalProvider$Api16Impl: android.os.CancellationSignal create()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemRowTitle(java.lang.Object)
io.flutter.embedding.android.FlutterImageView: android.media.ImageReader getImageReader()
com.google.android.gms.internal.measurement.zzfh$zzd$zzb: com.google.android.gms.internal.measurement.zzfh$zzd$zzb[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setTooltipText(android.view.MenuItem,java.lang.CharSequence)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeWidth(float)
io.flutter.embedding.engine.FlutterJNI: void nativeImageHeaderCallback(long,int,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownVerticalOffset()
io.flutter.plugins.firebase.crashlytics.FlutterError: FlutterError(java.lang.String)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.renderer.FlutterRenderer$ViewportMetrics getViewportMetrics()
androidx.core.view.ViewCompat$Api21Impl: java.lang.String getTransitionName(android.view.View)
io.flutter.plugins.firebase.core.FlutterFirebaseCorePlugin: FlutterFirebaseCorePlugin()
io.flutter.plugins.sharedpreferences.LegacySharedPreferencesPlugin: LegacySharedPreferencesPlugin()
android.support.v4.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.SpinnerAdapter)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getTooltipText(android.view.MenuItem)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMinTextSize()
com.google.firebase.analytics.FirebaseAnalytics: com.google.firebase.analytics.FirebaseAnalytics getInstance(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void nativeSetAccessibilityFeatures(long,int)
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier[] values()
androidx.appcompat.widget.Toolbar: int getContentInsetEnd()
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setImeVisibility(boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture)
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatEditText: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setDropDownBackgroundResource(int)
androidx.recyclerview.widget.RecyclerView: void setItemAnimator(androidx.recyclerview.widget.RecyclerView$ItemAnimator)
androidx.core.widget.NestedScrollView: NestedScrollView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportImageTintList()
androidx.appcompat.widget.Toolbar: void setTitleMarginBottom(int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: boolean canApplyTheme(android.graphics.drawable.Drawable)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType[] values()
androidx.appcompat.widget.SwitchCompat: void setSwitchPadding(int)
androidx.appcompat.widget.Toolbar: void setNavigationContentDescription(int)
com.google.firebase.sessions.SessionLifecycleService: SessionLifecycleService()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.FirebaseSessions getComponents$lambda-0(com.google.firebase.components.ComponentContainer)
androidx.appcompat.widget.SwitchCompat: void setThumbTintMode(android.graphics.PorterDuff$Mode)
com.google.firebase.sessions.api.SessionSubscriber$Name: com.google.firebase.sessions.api.SessionSubscriber$Name valueOf(java.lang.String)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$100(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.plugin.platform.PlatformViewWrapper: int getRenderTargetHeight()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivitySaveInstanceState(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzdi,long)
androidx.preference.SwitchPreferenceCompat: SwitchPreferenceCompat(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.ActionBarContextView: void setTitle(java.lang.CharSequence)
androidx.preference.internal.PreferenceImageView: int getMaxWidth()
androidx.appcompat.widget.Toolbar: int getContentInsetEndWithActions()
androidx.activity.OnBackPressedDispatcher$Api33Impl: void unregisterOnBackInvokedCallback(java.lang.Object,java.lang.Object)
io.flutter.view.AccessibilityViewEmbedder: android.view.View platformViewOfNode(int)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushClipRRect(int,int,int,int,float[])
androidx.appcompat.widget.Toolbar: void setCollapseIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: androidx.lifecycle.Lifecycle getLifecycle()
androidx.appcompat.widget.SwitchCompat: int getSwitchPadding()
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getTrackTintMode()
androidx.preference.internal.PreferenceImageView: void setMaxWidth(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginBottom()
io.flutter.embedding.android.FlutterView: android.view.accessibility.AccessibilityNodeProvider getAccessibilityNodeProvider()
io.flutter.embedding.engine.FlutterJNI: java.lang.String getObservatoryUri()
androidx.core.view.WindowInsetsCompat$TypeImpl30: int toPlatformType(int)
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setSelector(android.graphics.drawable.Drawable)
com.google.android.gms.measurement.AppMeasurementService: AppMeasurementService()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getParent(android.view.accessibility.AccessibilityNodeInfo,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void attachToGLContext(int)
androidx.core.widget.PopupWindowCompat$Api23Impl: int getWindowLayoutType(android.widget.PopupWindow)
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedPreScroll(android.view.ViewParent,android.view.View,int,int,int[])
androidx.recyclerview.widget.RecyclerView: void setChildDrawingOrderCallback(androidx.recyclerview.widget.RecyclerView$ChildDrawingOrderCallback)
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status[] values()
androidx.core.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.embedding.engine.FlutterJNI: void updateSemantics(java.nio.ByteBuffer,java.lang.String[],java.nio.ByteBuffer[])
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setSgtmDebugInfo(android.content.Intent)
androidx.core.view.ViewCompat$Api26Impl: void setAutofillHints(android.view.View,java.lang.String[])
androidx.appcompat.widget.Toolbar: android.view.Menu getMenu()
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype[] values()
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
androidx.versionedparcelable.CustomVersionedParcelable: CustomVersionedParcelable()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void maybeWaitOnFence(android.media.Image)
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserId(java.lang.String,long)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getScaleY()
androidx.appcompat.widget.AppCompatTextView: int getAutoSizeMinTextSize()
io.flutter.embedding.android.FlutterTextureView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: void authenticate(java.lang.Object,java.lang.Object,android.os.CancellationSignal,int,java.lang.Object,android.os.Handler)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getSystemGestureInsets()
android.support.v4.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void deferredComponentInstallFailure(int,java.lang.String,boolean)
androidx.core.view.ViewCompat$Api23Impl: int getScrollIndicators(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void notifyLowMemoryWarning()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityPaused(com.google.android.gms.dynamic.IObjectWrapper,long)
com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService: JobInfoSchedulerService()
io.flutter.embedding.engine.FlutterJNI: void nativeOnVsync(long,long,long)
com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture: com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture[] values()
androidx.core.content.ContextCompat$Api23Impl: int getColor(android.content.Context,int)
android.support.v4.graphics.drawable.IconCompatParcelizer: IconCompatParcelizer()
io.flutter.view.AccessibilityViewEmbedder: void copyAccessibilityFields(android.view.accessibility.AccessibilityNodeInfo,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatImageButton: void setSupportImageTintList(android.content.res.ColorStateList)
androidx.preference.internal.PreferenceImageView: int getMaxHeight()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
com.google.android.datatransport.cct.internal.QosTier: com.google.android.datatransport.cct.internal.QosTier valueOf(java.lang.String)
androidx.activity.Api34Impl: float progress(android.window.BackEvent)
androidx.appcompat.widget.SwitchCompat: int getThumbOffset()
androidx.appcompat.widget.ActionBarContextView: int getContentHeight()
io.flutter.view.TextureRegistry$SurfaceProducer: android.view.Surface getSurface()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View$OnApplyWindowInsetsListener getInsetsListener()
androidx.appcompat.widget.AppCompatTextView: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.appcompat.widget.Toolbar: void setTitleMarginEnd(int)
com.google.android.gms.internal.measurement.zzfn$zza$zze: com.google.android.gms.internal.measurement.zzfn$zza$zze[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEventAndBundle(java.lang.String,java.lang.String,android.os.Bundle,com.google.android.gms.internal.measurement.zzdi,long)
androidx.core.app.ActivityCompat$Api31Impl: boolean isLaunchedFromBubble(android.app.Activity)
androidx.core.graphics.drawable.IconCompat$Api28Impl: int getResId(java.lang.Object)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeSetViewportMetrics(long,float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap decodeImage(java.nio.ByteBuffer,long)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerWebSource(androidx.privacysandbox.ads.adservices.measurement.WebSourceRegistrationRequest,kotlin.coroutines.Continuation)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getThumbTintList()
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void nativeUnregisterTexture(long,long)
com.google.firebase.ktx.FirebaseCommonKtxRegistrar: java.util.List getComponents()
androidx.appcompat.widget.Toolbar: void setTitle(int)
androidx.core.content.ContextCompat$Api33Impl: android.content.Intent registerReceiver(android.content.Context,android.content.BroadcastReceiver,android.content.IntentFilter,java.lang.String,android.os.Handler,int)
androidx.biometric.CryptoObjectUtils$Api23Impl: void setEncryptionPaddingPKCS7(android.security.keystore.KeyGenParameterSpec$Builder)
io.flutter.view.AccessibilityBridge$AccessibilityFeature: io.flutter.view.AccessibilityBridge$AccessibilityFeature[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: FlutterRenderer$ImageTextureRegistryEntry(io.flutter.embedding.engine.renderer.FlutterRenderer,long)
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterImageTexture(long,long,java.lang.ref.WeakReference,boolean)
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState valueOf(java.lang.String)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getPivotX()
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat[] values()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsentThirdParty(android.os.Bundle,long)
com.google.firebase.installations.FirebaseInstallationsRegistrar: java.util.List getComponents()
androidx.core.app.ActivityCompat$Api23Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
androidx.appcompat.widget.SwitchCompat: void setSplitTrack(boolean)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void release()
androidx.appcompat.widget.Toolbar: void setTitleTextColor(android.content.res.ColorStateList)
androidx.appcompat.widget.LinearLayoutCompat: android.graphics.drawable.Drawable getDividerDrawable()
androidx.appcompat.widget.SearchView: int getInputType()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$502(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,android.view.WindowInsets)
io.flutter.embedding.engine.FlutterOverlaySurface: android.view.Surface getSurface()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: long id()
androidx.core.view.ViewCompat$Api21Impl: float getTranslationZ(android.view.View)
androidx.core.widget.EdgeEffectCompat$Api21Impl: void onPull(android.widget.EdgeEffect,float,float)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat computeSystemWindowInsets(android.view.View,androidx.core.view.WindowInsetsCompat,android.graphics.Rect)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: android.view.WindowInsets createWindowInsetsInstance()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: java.lang.String getCollectionItemColumnTitle(java.lang.Object)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void initialize(com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.internal.measurement.zzdq,long)
androidx.appcompat.widget.SearchView: void setOnSearchClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.LinearLayoutCompat: int getBaseline()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numTrims()
androidx.appcompat.widget.SearchView: void setQueryHint(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: void setTextMetricsParamsCompat(androidx.core.text.PrecomputedTextCompat$Params)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownHorizontalOffset(int)
io.flutter.embedding.android.FlutterView: io.flutter.embedding.engine.FlutterEngine getAttachedFlutterEngine()
androidx.core.app.ActivityCompat$Api31Impl: boolean shouldShowRequestPermissionRationale(android.app.Activity,java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int pendingDequeuedImages()
androidx.appcompat.widget.AppCompatTextView: void setPrecomputedText(androidx.core.text.PrecomputedTextCompat)
androidx.appcompat.widget.AppCompatTextView: void setLastBaselineToBottomHeight(int)
androidx.recyclerview.widget.RecyclerView: androidx.core.view.NestedScrollingChildHelper getScrollingChildHelper()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityResumed(com.google.android.gms.dynamic.IObjectWrapper,long)
androidx.core.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl23: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.core.view.ViewCompat$Api28Impl: void setScreenReaderFocusable(android.view.View,boolean)
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.view.TextureRegistry$SurfaceProducer$Callback access$200(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setUserProperty(java.lang.String,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,boolean,long)
androidx.appcompat.widget.Toolbar: void setLogoDescription(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatRadioButton: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.View access$400(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.view.AccessibilityViewEmbedder: android.view.accessibility.AccessibilityNodeInfo createAccessibilityNodeInfo(int)
androidx.appcompat.widget.AppCompatEditText: android.view.textclassifier.TextClassifier getTextClassifier()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMinimumSessionDuration(long)
androidx.appcompat.widget.ActionBarOverlayLayout: void setWindowCallback(android.view.Window$Callback)
androidx.core.view.DisplayCutoutCompat$Api28Impl: int getSafeInsetRight(android.view.DisplayCutout)
io.flutter.embedding.android.RenderMode: io.flutter.embedding.android.RenderMode valueOf(java.lang.String)
androidx.core.view.MenuItemCompat$Api26Impl: java.lang.CharSequence getContentDescription(android.view.MenuItem)
androidx.appcompat.widget.SwitchCompat: void setTrackTintList(android.content.res.ColorStateList)
androidx.core.view.ViewCompat$Api28Impl: boolean isScreenReaderFocusable(android.view.View)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownVerticalOffset(int)
com.google.android.gms.internal.measurement.zzdg: com.google.android.gms.internal.measurement.zzdd asInterface(android.os.IBinder)
androidx.appcompat.widget.LinearLayoutCompat: void setGravity(int)
androidx.recyclerview.widget.RecyclerView: void setScrollState(int)
androidx.core.view.ViewCompat$Api26Impl: android.view.View keyboardNavigationClusterSearch(android.view.View,android.view.View,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: androidx.core.graphics.PathParser$PathDataNode[] getPathData()
androidx.appcompat.widget.LinearLayoutCompat: void setWeightSum(float)
androidx.appcompat.widget.DialogTitle: DialogTitle(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void applyTransactions()
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeDestroy(long)
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.fragment.app.strictmode.FragmentStrictMode$Flag: androidx.fragment.app.strictmode.FragmentStrictMode$Flag[] values()
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
com.google.android.gms.internal.measurement.zzcl: com.google.android.gms.internal.measurement.zzcl[] values()
androidx.appcompat.widget.ActionBarContextView: void setContentHeight(int)
androidx.recyclerview.widget.RecyclerView: int getScrollState()
androidx.biometric.CryptoObjectUtils$Api23Impl: void initKeyGenerator(javax.crypto.KeyGenerator,android.security.keystore.KeyGenParameterSpec)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: java.lang.CharSequence getContainerTitle(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void onPreEngineRestart()
com.google.firebase.analytics.FirebaseAnalytics: java.lang.String getFirebaseInstanceId()
androidx.core.view.WindowInsetsCompat$Impl20: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.embedding.engine.FlutterJNI: void onBeginFrame()
androidx.core.view.ViewCompat$Api21Impl: void setTranslationZ(android.view.View,float)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodeParent(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState[] values()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setFirstBaselineToTopHeight(int)
androidx.appcompat.widget.AppCompatImageView: android.content.res.ColorStateList getSupportImageTintList()
com.google.firebase.sessions.DataCollectionState: com.google.firebase.sessions.DataCollectionState[] values()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedFling(android.view.ViewParent,android.view.View,float,float,boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPlatformMessage(long,java.lang.String,java.nio.ByteBuffer,int,int)
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatButton: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageEmptyResponseCallback(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: void setPathData(androidx.core.graphics.PathParser$PathDataNode[])
androidx.appcompat.widget.ViewStubCompat: android.view.LayoutInflater getLayoutInflater()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void scheduleFrame()
io.flutter.embedding.engine.FlutterJNI: void nativeMarkTextureFrameAvailable(long,long)
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeStableInsets()
androidx.appcompat.widget.ViewStubCompat: void setInflatedId(int)
androidx.activity.OnBackPressedDispatcher$Api33Impl: android.window.OnBackInvokedCallback createOnBackInvokedCallback(kotlin.jvm.functions.Function0)
androidx.core.graphics.Insets$Api29Impl: android.graphics.Insets of(int,int,int,int)
androidx.appcompat.view.menu.ListMenuItemView: void setGroupDividerEnabled(boolean)
androidx.appcompat.widget.ActionMenuView: ActionMenuView(android.content.Context,android.util.AttributeSet)
androidx.biometric.KeyguardUtils$Api23Impl: boolean isDeviceSecure(android.app.KeyguardManager)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void endRearDisplayPresentationSession()
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: void setPresentationView(android.view.View)
androidx.recyclerview.widget.RecyclerView: void setHasFixedSize(boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: androidx.core.view.WindowInsetsCompat build()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag[] values()
io.flutter.embedding.engine.FlutterJNI: android.graphics.Bitmap getBitmap()
androidx.core.view.ViewCompat$Api28Impl: void setAccessibilityPaneTitle(android.view.View,java.lang.CharSequence)
androidx.appcompat.widget.AppCompatRadioButton: void setSupportButtonTintList(android.content.res.ColorStateList)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.SessionGenerator getComponents$lambda-1(com.google.firebase.components.ComponentContainer)
androidx.profileinstaller.ProfileVerifier$Api33Impl: android.content.pm.PackageInfo getPackageInfo(android.content.pm.PackageManager,android.content.Context)
androidx.core.widget.ImageViewCompat$Api21Impl: void setImageTintList(android.widget.ImageView,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void setDeferredComponentManager(io.flutter.embedding.engine.deferredcomponents.DeferredComponentManager)
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintMode(android.view.View,android.graphics.PorterDuff$Mode)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: android.view.WindowInsets access$500(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityDelegate(io.flutter.embedding.engine.FlutterJNI$AccessibilityDelegate)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: android.graphics.SurfaceTexture surfaceTexture()
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchPointerDataPacket(long,java.nio.ByteBuffer,int)
io.flutter.embedding.engine.FlutterJNI: void destroyOverlaySurface2()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$RecycledViewPool getRecycledViewPool()
io.flutter.embedding.engine.FlutterJNI: void updateDisplayMetrics(int,float,float,float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushOpacity(float)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: void setOnDescendantFocusChangeListener(android.view.View$OnFocusChangeListener)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: float getFinalOpacity()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: void onPrepare(android.view.WindowInsetsAnimation)
androidx.appcompat.view.menu.ActionMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityStarted(android.app.Activity)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setTappableElementInsets(androidx.core.graphics.Insets)
io.flutter.view.TextureRegistry$SurfaceLifecycle: io.flutter.view.TextureRegistry$SurfaceLifecycle[] values()
io.flutter.embedding.engine.FlutterJNI: void setViewportMetrics(float,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int,int[],int[],int[])
androidx.core.app.RemoteActionCompatParcelizer: void write(androidx.core.app.RemoteActionCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmojiModifier(int)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.String[] getDigitStrings(android.icu.text.DecimalFormatSymbols)
androidx.appcompat.widget.ActionMenuView: void setPresenter(androidx.appcompat.widget.ActionMenuPresenter)
androidx.recyclerview.widget.RecyclerView: void setScrollingTouchSlop(int)
io.flutter.embedding.android.FlutterImageView$SurfaceKind: io.flutter.embedding.android.FlutterImageView$SurfaceKind valueOf(java.lang.String)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityStarted(android.app.Activity)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader()
androidx.appcompat.widget.AppCompatButton: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
androidx.appcompat.view.menu.ListMenuItemView: void setIcon(android.graphics.drawable.Drawable)
androidx.core.view.MenuItemCompat$Api26Impl: android.graphics.PorterDuff$Mode getIconTintMode(android.view.MenuItem)
androidx.appcompat.widget.SearchView: SearchView(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl20: boolean equals(java.lang.Object)
androidx.core.view.WindowInsetsCompat$Impl20: WindowInsetsCompat$Impl20(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl20)
io.flutter.view.TextureRegistry$SurfaceProducer: void scheduleFrame()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void endAdUnitExposure(java.lang.String,long)
androidx.appcompat.widget.SearchView: int getPreferredHeight()
androidx.appcompat.widget.Toolbar: void setOverflowIcon(android.graphics.drawable.Drawable)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: int getStrokeColor()
androidx.core.widget.EdgeEffectCompat$Api31Impl: float getDistance(android.widget.EdgeEffect)
com.google.android.datatransport.runtime.backends.BackendResponse$Status: com.google.android.datatransport.runtime.backends.BackendResponse$Status valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void pruneImageReaderQueue()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getAppInstanceId(com.google.android.gms.internal.measurement.zzdi)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportButtonTintList()
androidx.appcompat.widget.ActionBarOverlayLayout: void setLogo(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImageReaders()
androidx.appcompat.widget.AppCompatImageView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.appcompat.widget.SearchView: int getSuggestionRowLayout()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathStart()
net.nfet.flutter.printing.PrintFileProvider: PrintFileProvider()
com.google.firebase.installations.FirebaseInstallationsKtxRegistrar: FirebaseInstallationsKtxRegistrar()
androidx.datastore.preferences.protobuf.Writer$FieldOrder: androidx.datastore.preferences.protobuf.Writer$FieldOrder[] values()
androidx.core.view.WindowInsetsCompat$Impl21: WindowInsetsCompat$Impl21(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl21)
androidx.appcompat.widget.AppCompatSpinner: void setPrompt(java.lang.CharSequence)
androidx.appcompat.widget.AppCompatTextView: int getLastBaselineToBottomHeight()
io.flutter.embedding.engine.FlutterJNI: void setPlatformMessageHandler(io.flutter.embedding.engine.dart.PlatformMessageHandler)
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory getEdgeEffectFactory()
androidx.appcompat.view.menu.ListMenuItemView: void setCheckable(boolean)
androidx.core.view.ViewCompat$Api21Impl: boolean hasNestedScrollingParent(android.view.View)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$MobileSubtype valueOf(java.lang.String)
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeInvokePlatformMessageEmptyResponseCallback(long,int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.Object createRangeInfo(int,float,float,float)
com.google.android.gms.internal.measurement.zzkd: com.google.android.gms.internal.measurement.zzkd[] values()
io.flutter.embedding.engine.FlutterJNI: io.flutter.view.FlutterCallbackInformation nativeLookupCallbackInformation(long)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: android.view.WindowInsets onProgress(android.view.WindowInsets,java.util.List)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownWidth()
androidx.appcompat.widget.AppCompatSpinner: void setBackgroundResource(int)
androidx.core.view.ViewCompat$Api26Impl: void addKeyboardNavigationClusters(android.view.View,java.util.Collection,int)
androidx.lifecycle.Lifecycle$State: androidx.lifecycle.Lifecycle$State valueOf(java.lang.String)
android.support.v4.graphics.drawable.IconCompatParcelizer: void write(androidx.core.graphics.drawable.IconCompat,androidx.versionedparcelable.VersionedParcel)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: java.util.List getMutators()
io.flutter.embedding.engine.FlutterJNI: void requestDartDeferredLibrary(int)
com.google.firebase.analytics.FirebaseAnalytics: void setCurrentScreen(android.app.Activity,java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void setAsyncWaitForVsyncDelegate(io.flutter.embedding.engine.FlutterJNI$AsyncWaitForVsyncDelegate)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setAlphabeticShortcut(android.view.MenuItem,char,int)
androidx.core.widget.TextViewCompat$Api28Impl: java.lang.CharSequence castToCharSequence(android.text.PrecomputedText)
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task getPluginConstantsForFirebaseApp(com.google.firebase.FirebaseApp)
io.flutter.view.TextureRegistry$ImageTextureEntry: void release()
androidx.appcompat.widget.LinearLayoutCompat: void setHorizontalGravity(int)
androidx.appcompat.widget.ViewStubCompat: int getInflatedId()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportButtonTintMode(android.graphics.PorterDuff$Mode)
androidx.fragment.app.DialogFragment: DialogFragment()
androidx.profileinstaller.FileSectionType: androidx.profileinstaller.FileSectionType[] values()
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: boolean hasEnrolledFingerprints(java.lang.Object)
androidx.core.view.ViewCompat$Api29Impl: android.view.contentcapture.ContentCaptureSession getContentCaptureSession(android.view.View)
androidx.recyclerview.widget.RecyclerView: int getMinFlingVelocity()
com.google.android.gms.measurement.internal.zziq$zza: com.google.android.gms.measurement.internal.zziq$zza[] values()
androidx.core.widget.NestedScrollView: float getTopFadingEdgeStrength()
androidx.appcompat.widget.AppCompatTextView: void setTextFuture(java.util.concurrent.Future)
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: java.util.List getComponents()
androidx.biometric.BiometricFragment$Api28Impl: void setDescription(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence)
androidx.activity.Api34Impl: float touchX(android.window.BackEvent)
androidx.recyclerview.widget.RecyclerView: int getItemDecorationCount()
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundResource(int)
androidx.loader.app.LoaderManagerImpl$LoaderViewModel: LoaderManagerImpl$LoaderViewModel()
androidx.core.widget.NestedScrollView: void setNestedScrollingEnabled(boolean)
io.flutter.view.AccessibilityViewEmbedder: boolean requestSendAccessibilityEvent(android.view.View,android.view.View,android.view.accessibility.AccessibilityEvent)
androidx.appcompat.widget.AppCompatCheckBox: android.content.res.ColorStateList getSupportBackgroundTintList()
io.flutter.embedding.engine.FlutterJNI: void addIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.core.view.WindowInsetsCompat$Impl: void setStableInsets(androidx.core.graphics.Insets)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void resetAnalyticsData(long)
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: AppCompatTextViewAutoSizeHelper$Impl()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateX()
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType valueOf(java.lang.String)
androidx.preference.MultiSelectListPreference: MultiSelectListPreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getNavigationContentDescription()
io.flutter.view.AccessibilityViewEmbedder: AccessibilityViewEmbedder(android.view.View,int)
androidx.activity.Api34Impl: int swipeEdge(android.window.BackEvent)
androidx.appcompat.widget.AppCompatTextView: int[] getAutoSizeTextAvailableSizes()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.SessionLifecycleServiceBinder getComponents$lambda-5(com.google.firebase.components.ComponentContainer)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintMode(android.graphics.PorterDuff$Mode)
io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type: io.flutter.plugin.editing.TextInputPlugin$InputTarget$Type valueOf(java.lang.String)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType[] values()
io.flutter.embedding.engine.FlutterJNI: void invokePlatformMessageResponseCallback(int,java.nio.ByteBuffer,int)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostCreated(android.app.Activity,android.os.Bundle)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback$AnimationCallback: ImeSyncDeferringInsetsCallback$AnimationCallback(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.core.view.ViewCompat$Api21Impl: void callCompatInsetAnimationCallback(android.view.WindowInsets,android.view.View)
io.flutter.view.TextureRegistry$ImageTextureEntry: long id()
androidx.core.view.ViewCompat$Api21Impl: void setBackgroundTintList(android.view.View,android.content.res.ColorStateList)
androidx.appcompat.widget.AppCompatButton: void setBackgroundResource(int)
androidx.appcompat.widget.AppCompatImageView: void setSupportImageTintMode(android.graphics.PorterDuff$Mode)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointEmoji(int)
androidx.appcompat.widget.ButtonBarLayout: void setStacked(boolean)
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceCreated(long,android.view.Surface)
dev.fluttercommunity.plus.share.SharePlusPlugin: SharePlusPlugin()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: long getMinDurationBetweenContentChangeMillis(android.view.accessibility.AccessibilityNodeInfo)
com.google.android.gms.measurement.AppMeasurement: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setContainerTitle(android.view.accessibility.AccessibilityNodeInfo,java.lang.CharSequence)
androidx.core.app.ActivityCompat$Api23Impl: void onSharedElementsReady(java.lang.Object)
com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus: com.google.firebase.analytics.FirebaseAnalytics$ConsentStatus[] values()
androidx.lifecycle.ProcessLifecycleInitializer: ProcessLifecycleInitializer()
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void removeRearDisplayStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.LinearLayoutCompat: int getVirtualChildCount()
androidx.core.widget.CompoundButtonCompat$Api21Impl: android.content.res.ColorStateList getButtonTintList(android.widget.CompoundButton)
io.flutter.embedding.engine.FlutterJNI: void ensureRunningOnMainThread()
androidx.core.view.ViewCompat$Api28Impl: boolean isAccessibilityHeading(android.view.View)
io.flutter.embedding.engine.FlutterJNI: void prefetchDefaultFontManager()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableDelegateState: int getChangingConfigurations()
androidx.biometric.BiometricFragment$Api28Impl: void authenticate(android.hardware.biometrics.BiometricPrompt,android.os.CancellationSignal,java.util.concurrent.Executor,android.hardware.biometrics.BiometricPrompt$AuthenticationCallback)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setShortcut(android.view.MenuItem,char,char,int,int)
androidx.core.view.ViewCompat$Api20Impl: void requestApplyInsets(android.view.View)
androidx.core.widget.TextViewCompat$Api23Impl: android.graphics.PorterDuff$Mode getCompoundDrawableTintMode(android.widget.TextView)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$800(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer)
androidx.biometric.BiometricFragment$Api28Impl: android.hardware.biometrics.BiometricPrompt buildPrompt(android.hardware.biometrics.BiometricPrompt$Builder)
androidx.core.view.ViewCompat$Api26Impl: boolean isKeyboardNavigationCluster(android.view.View)
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Icon createWithAdaptiveBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.FlutterJNI: void cleanupMessageData(long)
androidx.appcompat.widget.ActionBarOverlayLayout: ActionBarOverlayLayout(android.content.Context,android.util.AttributeSet)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void registerPlugin(java.lang.String,io.flutter.plugins.firebase.core.FlutterFirebasePlugin)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType: io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack$FlutterMutatorType valueOf(java.lang.String)
androidx.appcompat.widget.FitWindowsFrameLayout: FitWindowsFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.core.widget.TextViewCompat$Api28Impl: void setFirstBaselineToTopHeight(android.widget.TextView,int)
io.flutter.view.TextureRegistry$SurfaceTextureEntry$-CC: void $default$setOnFrameConsumedListener(io.flutter.view.TextureRegistry$SurfaceTextureEntry,io.flutter.view.TextureRegistry$OnFrameConsumedListener)
kotlin.time.DurationUnit: kotlin.time.DurationUnit valueOf(java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: java.lang.Object requireViewById(android.view.View,int)
androidx.core.view.ViewCompat$Api26Impl: void setKeyboardNavigationCluster(android.view.View,boolean)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setStrokeAlpha(float)
androidx.core.view.ViewCompat$Api30Impl: boolean isImportantForContentCapture(android.view.View)
androidx.appcompat.widget.AppCompatImageView: void setImageBitmap(android.graphics.Bitmap)
io.flutter.embedding.engine.FlutterJNI: void setSemanticsEnabledInNative(boolean)
androidx.appcompat.widget.Toolbar: void setCollapseContentDescription(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int,boolean)
dev.fluttercommunity.plus.connectivity.ConnectivityPlugin: ConnectivityPlugin()
android.support.v4.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$300(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getCollapseIcon()
com.baseflow.permissionhandler.PermissionHandlerPlugin: PermissionHandlerPlugin()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: void setTitleMarginTop(int)
androidx.appcompat.widget.SwitchCompat: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.embedding.engine.FlutterJNI: void registerTexture(long,io.flutter.embedding.engine.renderer.SurfaceTextureWrapper)
androidx.appcompat.widget.SearchView: androidx.cursoradapter.widget.CursorAdapter getSuggestionsAdapter()
androidx.appcompat.widget.ContentFrameLayout: ContentFrameLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.LinearLayoutCompat: void setDividerPadding(int)
androidx.core.content.ContextCompat$Api28Impl: java.util.concurrent.Executor getMainExecutor(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl30: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.ActionBarOverlayLayout: void setOverlayMode(boolean)
io.flutter.view.TextureRegistry$SurfaceTextureEntry: long id()
androidx.appcompat.widget.AppCompatButton: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerWebTriggerAsync(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: android.util.DisplayMetrics getRearDisplayMetrics()
com.google.android.gms.measurement.AppMeasurement: com.google.android.gms.measurement.AppMeasurement getInstance(android.content.Context)
androidx.biometric.FingerprintDialogFragment$Api21Impl: void startAnimation(android.graphics.drawable.Drawable)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledVerticalScrollFactor(android.view.ViewConfiguration)
io.flutter.embedding.engine.FlutterJNI: void scheduleFrame()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: void computeAndSetTextDirection(android.text.StaticLayout$Builder,android.widget.TextView)
androidx.appcompat.widget.SwitchCompat: int getThumbTextPadding()
androidx.appcompat.widget.AppCompatSpinner: java.lang.CharSequence getPrompt()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void getBoundsInWindow(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect)
io.flutter.embedding.engine.FlutterJNI: void removeIsDisplayingFlutterUiListener(io.flutter.embedding.engine.renderer.FlutterUiDisplayListener)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillColor(int)
androidx.core.view.ViewConfigurationCompat$Api26Impl: float getScaledHorizontalScrollFactor(android.view.ViewConfiguration)
androidx.appcompat.widget.Toolbar: android.widget.TextView getTitleTextView()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.view.WindowInsetsCompat consumeSystemWindowInsets()
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection valueOf(java.lang.String)
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterOverlaySurface createOverlaySurface2()
androidx.preference.DropDownPreference: DropDownPreference(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api29Impl: android.view.View$AccessibilityDelegate getAccessibilityDelegate(android.view.View)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: ProcessLifecycleOwner$attach$1(androidx.lifecycle.ProcessLifecycleOwner)
com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior: com.google.firebase.crashlytics.internal.settings.SettingsCacheBehavior valueOf(java.lang.String)
androidx.appcompat.widget.ActionBarContainer: void setVisibility(int)
androidx.appcompat.widget.Toolbar: androidx.appcompat.widget.ActionMenuPresenter getOuterActionMenuPresenter()
io.flutter.view.TextureRegistry$SurfaceProducer: boolean handlesCropAndRotation()
io.flutter.view.TextureRegistry$SurfaceProducer: void setCallback(io.flutter.view.TextureRegistry$SurfaceProducer$Callback)
androidx.recyclerview.widget.RecyclerView: void setLayoutFrozen(boolean)
androidx.core.widget.TextViewCompat$Api28Impl: android.text.PrecomputedText$Params getTextMetricsParams(android.widget.TextView)
androidx.appcompat.widget.AppCompatRadioButton: android.graphics.PorterDuff$Mode getSupportButtonTintMode()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHideOnContentScrollEnabled(boolean)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceCreated(android.view.Surface)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void applyTheme(android.graphics.drawable.Drawable,android.content.res.Resources$Theme)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getSubtitle()
androidx.core.graphics.drawable.DrawableCompat$Api23Impl: boolean setLayoutDirection(android.graphics.drawable.Drawable,int)
androidx.core.view.WindowInsetsCompat$Impl28: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.Toolbar: void setSubtitleTextColor(int)
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: androidx.core.text.PrecomputedTextCompat$Params getTextMetricsParamsCompat()
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness[] values()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl: boolean isHorizontallyScrollable(android.widget.TextView)
androidx.appcompat.view.menu.ListMenuItemView: android.view.LayoutInflater getInflater()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintMode(android.graphics.drawable.Drawable,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl20: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType: io.flutter.embedding.engine.systemchannels.PlatformChannel$SoundType[] values()
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void inflate(android.graphics.drawable.Drawable,android.content.res.Resources,org.xmlpull.v1.XmlPullParser,android.util.AttributeSet,android.content.res.Resources$Theme)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureState valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.ImageReader createImageReader33()
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
androidx.core.graphics.drawable.IconCompat$Api26Impl: android.graphics.drawable.Drawable createAdaptiveIconDrawable(android.graphics.drawable.Drawable,android.graphics.drawable.Drawable)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: void setTextSelectable(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.core.view.DisplayCutoutCompat$Api28Impl: android.view.DisplayCutout createDisplayCutout(android.graphics.Rect,java.util.List)
androidx.core.view.ViewCompat$Api21Impl: void setNestedScrollingEnabled(android.view.View,boolean)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityCreated(android.app.Activity,android.os.Bundle)
androidx.core.view.ViewCompat$Api21Impl: androidx.core.view.WindowInsetsCompat getRootWindowInsets(android.view.View)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void startRearDisplaySession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOff()
androidx.core.view.ViewCompat$Api26Impl: int getImportantForAutofill(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: android.media.Image acquireLatestImage()
io.flutter.plugins.sharedpreferences.StringListLookupResultType: io.flutter.plugins.sharedpreferences.StringListLookupResultType[] values()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnFrameConsumedListener(io.flutter.view.TextureRegistry$OnFrameConsumedListener)
androidx.core.view.ViewCompat$Api23Impl: void setScrollIndicators(android.view.View,int)
androidx.appcompat.widget.SearchView: void setSuggestionsAdapter(androidx.cursoradapter.widget.CursorAdapter)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPreDestroyed(android.app.Activity)
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture getMeasurementApiStatusAsync()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: float getTranslateY()
io.flutter.embedding.engine.FlutterJNI: long nativeAttach(io.flutter.embedding.engine.FlutterJNI)
androidx.core.view.ViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getBackgroundTintMode(android.view.View)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: void setRootAlpha(int)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager getFingerprintManagerOrNull(android.content.Context)
androidx.core.view.WindowInsetsCompat$Impl: WindowInsetsCompat$Impl(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.ButtonBarLayout: ButtonBarLayout(android.content.Context,android.util.AttributeSet)
com.google.firebase.sessions.FirebaseSessionsRegistrar: java.util.List getComponents()
androidx.core.view.WindowInsetsCompat$Impl: void setOverriddenInsets(androidx.core.graphics.Insets[])
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory valueOf(java.lang.String)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action[] values()
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationHelp(int,java.lang.CharSequence)
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason[] values()
com.google.firebase.datatransport.TransportRegistrar: java.util.List getComponents()
androidx.recyclerview.widget.RecyclerView: void setOnFlingListener(androidx.recyclerview.widget.RecyclerView$OnFlingListener)
androidx.core.app.AppOpsManagerCompat$Api29Impl: int checkOpNoThrow(android.app.AppOpsManager,java.lang.String,int,java.lang.String)
androidx.core.view.ViewCompat$Api28Impl: void addOnUnhandledKeyEventListener(android.view.View,androidx.core.view.ViewCompat$OnUnhandledKeyEventListenerCompat)
io.flutter.embedding.engine.FlutterJNI: void updateCustomAccessibilityActions(java.nio.ByteBuffer,java.lang.String[])
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setInstanceIdProvider(com.google.android.gms.internal.measurement.zzdo)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintList(android.widget.TextView,android.content.res.ColorStateList)
androidx.core.view.WindowInsetsCompat$Impl: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.appcompat.widget.LinearLayoutCompat: void setBaselineAligned(boolean)
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar: java.util.List getComponents()
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPath: java.lang.String getPathName()
androidx.appcompat.widget.ActionMenuView: android.graphics.drawable.Drawable getOverflowIcon()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerSourceAsync(android.net.Uri,android.view.InputEvent)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.datastore.preferences.protobuf.WireFormat$JavaType: androidx.datastore.preferences.protobuf.WireFormat$JavaType valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatTextView: void setAutoSizeTextTypeWithDefaults(int)
androidx.window.area.reflectionguard.ExtensionWindowAreaPresentationRequirements: android.content.Context getPresentationContext()
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding[] values()
com.google.android.gms.measurement.AppMeasurement: java.util.List getConditionalUserProperties(java.lang.String,java.lang.String)
io.flutter.view.AccessibilityViewEmbedder: void setFlutterNodesTranslateBounds(android.view.accessibility.AccessibilityNodeInfo,android.graphics.Rect,android.view.accessibility.AccessibilityNodeInfo)
com.google.android.gms.measurement.AppMeasurement: long generateEventId()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceChanged(long,int,int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VPathRenderer: float getAlpha()
androidx.core.view.ViewParentCompat$Api21Impl: void onNestedScrollAccepted(android.view.ViewParent,android.view.View,android.view.View,int)
androidx.core.view.VelocityTrackerCompat$Api34Impl: boolean isAxisSupported(android.view.VelocityTracker,int)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void getTransformMatrix(float[])
androidx.core.content.ContextCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
androidx.appcompat.view.menu.ListMenuItemView: void setSubMenuArrowVisible(boolean)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
io.flutter.embedding.engine.FlutterJNI: void nativeInit(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDefaultEventParameters(android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceWindowChanged(android.view.Surface)
com.google.android.datatransport.cct.CctBackendFactory: com.google.android.datatransport.runtime.backends.TransportBackend create(com.google.android.datatransport.runtime.backends.CreationContext)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getInsets(int)
androidx.appcompat.widget.SwitchCompat: void setTextOff(java.lang.CharSequence)
androidx.recyclerview.widget.RecyclerView: boolean getPreserveFocusAfterLayout()
com.google.android.gms.measurement.AppMeasurement: java.lang.String getCurrentScreenClass()
androidx.appcompat.widget.Toolbar: Toolbar(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.SearchView: int getSuggestionCommitIconResId()
com.google.firebase.sessions.api.SessionSubscriber$Name: com.google.firebase.sessions.api.SessionSubscriber$Name[] values()
androidx.core.view.WindowInsetsCompat$Impl28: boolean equals(java.lang.Object)
androidx.core.app.AppOpsManagerCompat$Api29Impl: java.lang.String getOpPackageName(android.content.Context)
io.flutter.embedding.engine.FlutterJNI: void registerImageTexture(long,io.flutter.view.TextureRegistry$ImageConsumer,boolean)
io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice: io.flutter.plugins.imagepicker.ImagePickerDelegate$CameraDevice valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeStepGranularity()
androidx.lifecycle.ProcessLifecycleOwner$attach$1$onActivityPreCreated$1: ProcessLifecycleOwner$attach$1$onActivityPreCreated$1(androidx.lifecycle.ProcessLifecycleOwner)
com.google.firebase.crashlytics.internal.common.DeliveryMechanism: com.google.firebase.crashlytics.internal.common.DeliveryMechanism[] values()
androidx.appcompat.widget.SearchView: int getPreferredWidth()
androidx.appcompat.widget.AppCompatImageView: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.recyclerview.widget.RecyclerView: void setEdgeEffectFactory(androidx.recyclerview.widget.RecyclerView$EdgeEffectFactory)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getRootStableInsets()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerViewAccessibilityDelegate getCompatAccessibilityDelegate()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture deleteRegistrationsAsync(androidx.privacysandbox.ads.adservices.measurement.DeletionRequest)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMajor()
androidx.window.layout.adapter.sidecar.DistinctElementSidecarCallback: void onWindowLayoutChanged(android.os.IBinder,androidx.window.sidecar.SidecarWindowLayoutInfo)
androidx.core.view.WindowInsetsCompat$Impl29: void setStableInsets(androidx.core.graphics.Insets)
androidx.core.graphics.drawable.IconCompat$Api23Impl: android.graphics.drawable.Drawable loadDrawable(android.graphics.drawable.Icon,android.content.Context)
com.google.android.gms.internal.measurement.zzgb$zzd$zza: com.google.android.gms.internal.measurement.zzgb$zzd$zza[] values()
io.flutter.plugins.localauth.Messages$AuthClassification: io.flutter.plugins.localauth.Messages$AuthClassification valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceChanged(int,int)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.datastore.preferences.protobuf.ProtoSyntax: androidx.datastore.preferences.protobuf.ProtoSyntax valueOf(java.lang.String)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode valueOf(java.lang.String)
androidx.core.view.VelocityTrackerCompat$Api34Impl: float getAxisVelocity(android.view.VelocityTracker,int,int)
androidx.appcompat.widget.AppCompatButton: void setSupportAllCaps(boolean)
io.flutter.embedding.engine.FlutterJNI: boolean isCodePointRegionalIndicator(int)
io.flutter.embedding.engine.FlutterJNI: void markTextureFrameAvailable(long)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.app.RemoteActionCompatParcelizer: androidx.core.app.RemoteActionCompat read(androidx.versionedparcelable.VersionedParcel)
kotlin.time.DurationUnit: kotlin.time.DurationUnit[] values()
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(int)
androidx.core.os.BundleApi21ImplKt: void putSizeF(android.os.Bundle,java.lang.String,android.util.SizeF)
androidx.datastore.preferences.protobuf.FieldType: androidx.datastore.preferences.protobuf.FieldType valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: AppMeasurementDynamiteService()
androidx.appcompat.widget.SearchView: void setOnCloseListener(androidx.appcompat.widget.SearchView$OnCloseListener)
androidx.appcompat.widget.ViewStubCompat: void setLayoutResource(int)
androidx.biometric.BiometricFragment$Api29Impl: void setDeviceCredentialAllowed(android.hardware.biometrics.BiometricPrompt$Builder,boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void loadReflectionField()
com.google.firebase.crashlytics.ktx.FirebaseCrashlyticsKtxRegistrar: FirebaseCrashlyticsKtxRegistrar()
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: androidx.window.extensions.area.ExtensionWindowAreaPresentation getRearDisplayPresentation()
androidx.appcompat.widget.Toolbar: int getTitleMarginStart()
io.flutter.embedding.engine.FlutterJNI: void setLocalizationPlugin(io.flutter.plugin.localization.LocalizationPlugin)
androidx.window.extensions.core.util.function.Predicate: boolean test(java.lang.Object)
io.flutter.embedding.engine.FlutterJNI: void onSurfaceDestroyed()
androidx.appcompat.widget.AppCompatSpinner: android.graphics.drawable.Drawable getPopupBackground()
androidx.core.view.ViewCompat$Api21Impl: void stopNestedScroll(android.view.View)
androidx.appcompat.widget.ActionBarContextView: java.lang.CharSequence getTitle()
androidx.appcompat.widget.ViewStubCompat: void setOnInflateListener(androidx.appcompat.widget.ViewStubCompat$OnInflateListener)
androidx.appcompat.widget.ActionBarContextView: int getAnimatedVisibility()
androidx.datastore.preferences.PreferencesProto$Value$ValueCase: androidx.datastore.preferences.PreferencesProto$Value$ValueCase[] values()
androidx.appcompat.widget.SwitchCompat: java.lang.CharSequence getTextOn()
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerWebTrigger(androidx.privacysandbox.ads.adservices.measurement.WebTriggerRegistrationRequest,kotlin.coroutines.Continuation)
io.flutter.plugin.platform.SingleViewPresentation: io.flutter.plugin.platform.SingleViewPresentation$PresentationState detachState()
io.flutter.view.AccessibilityViewEmbedder: void addChildrenToFlutterNode(android.view.accessibility.AccessibilityNodeInfo,android.view.View,android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.AppCompatSpinner: int getDropDownHorizontalOffset()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenName(com.google.android.gms.internal.measurement.zzdi)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspotBounds(android.graphics.drawable.Drawable,int,int,int,int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void lambda$dequeueImage$0()
androidx.core.view.WindowInsetsCompat$BuilderImpl: androidx.core.view.WindowInsetsCompat build()
androidx.appcompat.widget.SearchView: SearchView(android.content.Context,android.util.AttributeSet,int)
androidx.core.widget.TextViewCompat$Api23Impl: void setBreakStrategy(android.widget.TextView,int)
androidx.biometric.CryptoObjectUtils$Api23Impl: android.security.keystore.KeyGenParameterSpec$Builder createKeyGenParameterSpecBuilder(java.lang.String,int)
com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType: com.google.android.datatransport.cct.internal.NetworkConnectionInfo$NetworkType[] values()
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatImageView: void setImageResource(int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivitySaveInstanceState(android.app.Activity,android.os.Bundle)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeTextType()
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateJavaAssetManager(long,android.content.res.AssetManager,java.lang.String)
io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat: io.flutter.embedding.engine.systemchannels.PlatformChannel$ClipboardContentFormat valueOf(java.lang.String)
io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
androidx.appcompat.widget.AppCompatCheckBox: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.ViewGroupCompat$Api21Impl: boolean isTransitionGroup(android.view.ViewGroup)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: boolean shouldUpdate()
androidx.biometric.PackageUtils$Api23Impl: boolean hasSystemFeatureFingerprint(android.content.pm.PackageManager)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMajor()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: double deltaMillis(long)
io.flutter.embedding.engine.FlutterJNI: void nativeDispatchEmptyPlatformMessage(long,java.lang.String,int)
androidx.core.view.ViewCompat$Api26Impl: int getNextClusterForwardId(android.view.View)
androidx.core.view.MenuItemCompat$Api26Impl: int getNumericModifiers(android.view.MenuItem)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.FlutterJNI: void unregisterTexture(long)
androidx.lifecycle.SavedStateHandlesVM: SavedStateHandlesVM()
androidx.appcompat.widget.SwitchCompat: void setThumbResource(int)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void release()
io.flutter.plugins.imagepicker.ImagePickerCache$CacheType: io.flutter.plugins.imagepicker.ImagePickerCache$CacheType valueOf(java.lang.String)
kotlin.random.Random: Random()
androidx.appcompat.widget.AppCompatImageView: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.window.layout.util.ContextCompatHelperApi30: androidx.core.view.WindowInsetsCompat currentWindowInsets(android.content.Context)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getActiveReader()
androidx.appcompat.widget.SwitchCompat: int getSwitchMinWidth()
androidx.appcompat.widget.SearchView: void setAppSearchData(android.os.Bundle)
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.SessionDatastore getComponents$lambda-4(com.google.firebase.components.ComponentContainer)
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedScroll(android.view.View,int,int,int,int,int[])
androidx.recyclerview.widget.RecyclerView: void setPreserveFocusAfterLayout(boolean)
io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation: io.flutter.embedding.engine.systemchannels.PlatformChannel$DeviceOrientation[] values()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.view.Surface getSurface()
androidx.core.view.DisplayCutoutCompat$Api28Impl: java.util.List getBoundingRects(android.view.DisplayCutout)
androidx.core.view.ViewCompat$Api26Impl: void setTooltipText(android.view.View,java.lang.CharSequence)
com.google.android.gms.measurement.AppMeasurement: int getMaxUserProperties(java.lang.String)
com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver: AlarmManagerSchedulerBroadcastReceiver()
io.flutter.embedding.engine.FlutterJNI: void updateJavaAssetManager(android.content.res.AssetManager,java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setCurrentScreen(com.google.android.gms.dynamic.IObjectWrapper,java.lang.String,java.lang.String,long)
androidx.core.view.WindowInsetsCompat$Impl28: WindowInsetsCompat$Impl28(androidx.core.view.WindowInsetsCompat,androidx.core.view.WindowInsetsCompat$Impl28)
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: AuthenticationCallbackProvider$Api28Impl$1(androidx.biometric.AuthenticationCallbackProvider$Listener)
androidx.appcompat.widget.AppCompatTextView: int getFirstBaselineToTopHeight()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: void setOnTrimMemoryListener(io.flutter.view.TextureRegistry$OnTrimMemoryListener)
androidx.core.view.WindowInsetsCompat$Impl: int hashCode()
com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason: com.google.android.datatransport.runtime.firebase.transport.LogEventDropped$Reason valueOf(java.lang.String)
androidx.biometric.BiometricFragment$Api21Impl: android.content.Intent createConfirmDeviceCredentialIntent(android.app.KeyguardManager,java.lang.CharSequence,java.lang.CharSequence)
androidx.biometric.BiometricFragment$Api28Impl: void setNegativeButton(android.hardware.biometrics.BiometricPrompt$Builder,java.lang.CharSequence,java.util.concurrent.Executor,android.content.DialogInterface$OnClickListener)
androidx.recyclerview.widget.RecyclerView: void setOnScrollListener(androidx.recyclerview.widget.RecyclerView$OnScrollListener)
androidx.appcompat.widget.ActionBarContainer: void setTransitioning(boolean)
androidx.appcompat.widget.Toolbar: android.content.Context getPopupContext()
androidx.preference.ListPreference: ListPreference(android.content.Context,android.util.AttributeSet)
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(javax.crypto.Cipher)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.app.AppOpsManagerCompat$Api23Impl: int noteProxyOp(android.app.AppOpsManager,java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmoji(int)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.WindowInsetsCompat consumeDisplayCutout()
androidx.core.view.ViewCompat$Api29Impl: void setContentCaptureSession(android.view.View,androidx.core.view.contentcapture.ContentCaptureSessionCompat)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getInsets(int)
io.flutter.embedding.android.TransparencyMode: io.flutter.embedding.android.TransparencyMode[] values()
androidx.appcompat.widget.AppCompatButton: int[] getAutoSizeTextAvailableSizes()
io.flutter.embedding.engine.FlutterJNI: void ensureAttachedToNative()
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeaturesInNative(int)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: boolean isAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo)
androidx.privacysandbox.ads.adservices.internal.AdServicesInfo$Extensions30Impl: int getAdServicesVersion()
io.flutter.embedding.engine.FlutterJNI: void nativeSetSemanticsEnabled(long,boolean)
androidx.window.core.VerificationMode: androidx.window.core.VerificationMode[] values()
androidx.exifinterface.media.ExifInterfaceUtils$Api21Impl: void close(java.io.FileDescriptor)
io.flutter.plugins.firebase.core.FlutterFirebasePluginRegistry: void lambda$didReinitializeFirebaseCore$1(com.google.android.gms.tasks.TaskCompletionSource)
androidx.appcompat.widget.AlertDialogLayout: AlertDialogLayout(android.content.Context,android.util.AttributeSet)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getTappableElementInsets()
io.flutter.embedding.engine.FlutterJNI: void nativeSurfaceDestroyed(long)
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection[] values()
kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState: kotlinx.coroutines.scheduling.CoroutineScheduler$WorkerState[] values()
androidx.recyclerview.widget.RecyclerView: boolean getClipToPadding()
io.flutter.embedding.engine.FlutterJNI: void onRenderingStopped()
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: android.graphics.Matrix getFinalMatrix()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
com.example.wargani.MainActivity: MainActivity()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness[] values()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportImageTintMode()
androidx.core.widget.NestedScrollView: int getScrollRange()
androidx.core.view.ViewParentCompat$Api21Impl: void onStopNestedScroll(android.view.ViewParent,android.view.View)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30()
androidx.core.widget.CompoundButtonCompat$Api23Impl: android.graphics.drawable.Drawable getButtonDrawable(android.widget.CompoundButton)
androidx.appcompat.view.menu.ListMenuItemView: ListMenuItemView(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsRegionalIndicator(int)
androidx.core.app.RemoteActionCompatParcelizer: RemoteActionCompatParcelizer()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemGestureInsets()
androidx.appcompat.widget.Toolbar: int getContentInsetLeft()
com.tekartik.sqflite.SqflitePlugin: SqflitePlugin()
kotlinx.coroutines.CoroutineStart: kotlinx.coroutines.CoroutineStart valueOf(java.lang.String)
androidx.appcompat.widget.FitWindowsFrameLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState: com.google.firebase.concurrent.SequentialExecutor$WorkerRunningState valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void addEngineLifecycleListener(io.flutter.embedding.engine.FlutterEngine$EngineLifecycleListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType: io.flutter.embedding.engine.renderer.FlutterRenderer$DisplayFeatureType[] values()
com.google.firebase.encoders.proto.Protobuf$IntEncoding: com.google.firebase.encoders.proto.Protobuf$IntEncoding valueOf(java.lang.String)
androidx.appcompat.widget.SearchView: void setIconifiedByDefault(boolean)
androidx.core.widget.TextViewCompat$Api23Impl: void setHyphenationFrequency(android.widget.TextView,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getTestFlag(com.google.android.gms.internal.measurement.zzdi,int)
androidx.biometric.FingerprintDialogFragment$Api26Impl: int getColorErrorAttr()
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void unregisterOnMeasurementEventListener(com.google.android.gms.internal.measurement.zzdj)
androidx.appcompat.widget.ButtonBarLayout: void setAllowStacking(boolean)
androidx.appcompat.widget.LinearLayoutCompat: void setVerticalGravity(int)
androidx.appcompat.widget.ActionMenuView: void setOverflowReserved(boolean)
com.google.firebase.crashlytics.CrashlyticsRegistrar: CrashlyticsRegistrar()
androidx.core.view.ViewConfigurationCompat$Api28Impl: boolean shouldShowMenuShortcutsWhenKeyboardPresent(android.view.ViewConfiguration)
androidx.recyclerview.widget.GridLayoutManager: GridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.appcompat.widget.AppCompatTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
androidx.appcompat.widget.AppCompatButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.ActionMenuView: void setExpandedActionViewsExclusive(boolean)
androidx.recyclerview.widget.StaggeredGridLayoutManager: StaggeredGridLayoutManager(android.content.Context,android.util.AttributeSet,int,int)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemGestureInsets(androidx.core.graphics.Insets)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api30Impl: java.lang.CharSequence getStateDescription(android.view.accessibility.AccessibilityNodeInfo)
io.flutter.embedding.engine.FlutterJNI: void nativeRunBundleAndSnapshotFromLibrary(long,java.lang.String,java.lang.String,java.lang.String,android.content.res.AssetManager,java.util.List,long)
io.flutter.embedding.engine.FlutterJNI: void nativeUpdateDisplayMetrics(long)
androidx.biometric.CryptoObjectUtils$Api30Impl: android.security.identity.IdentityCredential getIdentityCredential(android.hardware.biometrics.BiometricPrompt$CryptoObject)
io.flutter.embedding.android.FlutterSurfaceView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType[] values()
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera valueOf(java.lang.String)
androidx.activity.Api34Impl: float touchY(android.window.BackEvent)
androidx.appcompat.widget.SwitchCompat: void setSwitchTypeface(android.graphics.Typeface)
io.flutter.embedding.android.FlutterView: io.flutter.plugin.common.BinaryMessenger getBinaryMessenger()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreFling(android.view.View,float,float)
com.google.android.gms.internal.measurement.zzfh$zzf$zza: com.google.android.gms.internal.measurement.zzfh$zzf$zza[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean dispatchNestedPreScroll(android.view.View,int,int,int[],int[])
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getCurrentScreenClass(com.google.android.gms.internal.measurement.zzdi)
androidx.appcompat.widget.SearchView: void setQueryRefinementEnabled(boolean)
androidx.appcompat.widget.Toolbar: int getTitleMarginTop()
androidx.core.widget.ImageViewCompat$Api21Impl: android.content.res.ColorStateList getImageTintList(android.widget.ImageView)
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportCompoundDrawablesTintList()
androidx.core.view.ViewCompat$Api21Impl$1: android.view.WindowInsets onApplyWindowInsets(android.view.View,android.view.WindowInsets)
androidx.appcompat.widget.ActionMenuView: int getWindowAnimations()
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController(io.flutter.plugin.platform.PlatformViewsController)
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getFixedHeightMinor()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityDestroyed(android.app.Activity)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: android.view.accessibility.AccessibilityNodeInfo$AccessibilityAction getActionScrollInDirection()
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void startRearDisplayPresentationSession(android.app.Activity,androidx.window.extensions.core.util.function.Consumer)
androidx.core.view.WindowInsetsCompat$Impl: void setRootViewData(androidx.core.graphics.Insets)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: SurfaceTextureWrapper(android.graphics.SurfaceTexture,java.lang.Runnable)
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPrePaused(android.app.Activity)
androidx.appcompat.view.menu.ExpandedMenuView: ExpandedMenuView(android.content.Context,android.util.AttributeSet)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult[] values()
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.appcompat.widget.SwitchCompat: SwitchCompat(android.content.Context,android.util.AttributeSet)
androidx.core.view.ViewCompat$Api29Impl: java.util.List getSystemGestureExclusionRects(android.view.View)
androidx.appcompat.widget.Toolbar: void setPopupTheme(int)
io.flutter.view.TextureRegistry$ImageTextureEntry: void pushImage(android.media.Image)
androidx.preference.TwoStatePreference: TwoStatePreference(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.AppCompatTextView: void setSupportBackgroundTintList(android.content.res.ColorStateList)
androidx.appcompat.widget.SearchView: void setSubmitButtonEnabled(boolean)
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
io.flutter.plugin.platform.PlatformViewWrapper: void setLayoutParams(android.widget.FrameLayout$LayoutParams)
androidx.core.widget.TextViewCompat$Api23Impl: int getHyphenationFrequency(android.widget.TextView)
androidx.appcompat.view.menu.ExpandedMenuView: int getWindowAnimations()
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: void finalize()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.view.DisplayCutoutCompat getDisplayCutout()
androidx.appcompat.widget.SwitchCompat: void setThumbPosition(float)
io.flutter.embedding.engine.FlutterJNI: void setAccessibilityFeatures(int)
androidx.appcompat.widget.ActionBarContextView: void setSubtitle(java.lang.CharSequence)
androidx.core.view.WindowInsetsCompat$Impl20: void copyRootViewBounds(android.view.View)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: boolean isTextSelectable(android.view.accessibility.AccessibilityNodeInfo)
androidx.appcompat.widget.Toolbar: int getPopupTheme()
androidx.core.view.WindowInsetsCompat$BuilderImpl: WindowInsetsCompat$BuilderImpl(androidx.core.view.WindowInsetsCompat)
androidx.biometric.AuthenticationCallbackProvider$Api30Impl: int getAuthenticationType(android.hardware.biometrics.BiometricPrompt$AuthenticationResult)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setMeasurementEnabled(boolean,long)
androidx.appcompat.widget.AppCompatSpinner: void setDropDownWidth(int)
androidx.appcompat.widget.Toolbar: int getTitleMarginEnd()
androidx.appcompat.widget.SwitchCompat: void setTrackTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl29: androidx.core.view.WindowInsetsCompat inset(int,int,int,int)
androidx.appcompat.widget.LinearLayoutCompat: int getDividerPadding()
androidx.appcompat.widget.LinearLayoutCompat: void setMeasureWithLargestChildEnabled(boolean)
androidx.core.content.res.ResourcesCompat$Api23Impl: android.content.res.ColorStateList getColorStateList(android.content.res.Resources,int,android.content.res.Resources$Theme)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean handlesCropAndRotation()
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: int getWindowAreaStatus()
androidx.core.widget.PopupWindowCompat$Api23Impl: void setOverlapAnchor(android.widget.PopupWindow,boolean)
androidx.core.view.WindowInsetsCompat$BuilderImpl30: WindowInsetsCompat$BuilderImpl30(androidx.core.view.WindowInsetsCompat)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logEvent(java.lang.String,java.lang.String,android.os.Bundle,boolean,boolean,long)
androidx.appcompat.widget.ActionBarOverlayLayout: java.lang.CharSequence getTitle()
io.flutter.plugins.firebase.core.FlutterFirebasePlugin: com.google.android.gms.tasks.Task didReinitializeFirebaseCore()
com.google.android.gms.internal.measurement.zzfv: com.google.android.gms.internal.measurement.zzfv[] values()
androidx.core.view.ViewCompat$Api21Impl: boolean isImportantForAccessibility(android.view.View)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void waitOnFence(android.media.Image)
androidx.appcompat.widget.AppCompatImageButton: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.AppCompatRadioButton: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.biometric.FingerprintDialogFragment: FingerprintDialogFragment()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void onActivityCreated(com.google.android.gms.dynamic.IObjectWrapper,android.os.Bundle,long)
androidx.appcompat.widget.SwitchCompat: int getCompoundPaddingRight()
androidx.biometric.AuthenticationCallbackProvider$Api28Impl$1: void onAuthenticationSucceeded(android.hardware.biometrics.BiometricPrompt$AuthenticationResult)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: android.media.Image acquireLatestImage()
androidx.appcompat.widget.AppCompatTextViewAutoSizeHelper$Impl29: AppCompatTextViewAutoSizeHelper$Impl29()
androidx.appcompat.widget.SwitchCompat: void setThumbTintList(android.content.res.ColorStateList)
androidx.core.content.res.ResourcesCompat$Api21Impl: android.graphics.drawable.Drawable getDrawableForDensity(android.content.res.Resources,int,int,android.content.res.Resources$Theme)
com.google.common.collect.Maps$EntryFunction: com.google.common.collect.Maps$EntryFunction[] values()
com.google.common.collect.BaseImmutableMultimap: BaseImmutableMultimap()
io.flutter.embedding.engine.FlutterOverlaySurface: int getId()
androidx.appcompat.widget.LinearLayoutCompat: int getGravity()
androidx.recyclerview.widget.RecyclerView: long getNanoTime()
com.google.firebase.components.ComponentDiscoveryService: ComponentDiscoveryService()
com.google.android.gms.internal.measurement.zzjq: com.google.android.gms.internal.measurement.zzjq[] values()
com.google.android.gms.measurement.AppMeasurementReceiver: AppMeasurementReceiver()
androidx.core.os.UserManagerCompat$Api24Impl: boolean isUserUnlocked(android.content.Context)
com.google.android.gms.measurement.internal.zzai: com.google.android.gms.measurement.internal.zzai[] values()
com.google.android.datatransport.Priority: com.google.android.datatransport.Priority valueOf(java.lang.String)
com.google.firebase.installations.remote.InstallationResponse$ResponseCode: com.google.firebase.installations.remote.InstallationResponse$ResponseCode[] values()
androidx.appcompat.widget.AppCompatRadioButton: void setBackgroundResource(int)
androidx.appcompat.widget.SwitchCompat: android.graphics.PorterDuff$Mode getThumbTintMode()
androidx.appcompat.view.menu.ActionMenuItemView: void setPopupCallback(androidx.appcompat.view.menu.ActionMenuItemView$PopupCallback)
androidx.appcompat.widget.AppCompatSpinner: void setAdapter(android.widget.Adapter)
io.flutter.embedding.engine.FlutterJNI: boolean nativeFlutterTextUtilsIsEmojiModifierBase(int)
androidx.biometric.BiometricFragment$Api30Impl: void setAllowedAuthenticators(android.hardware.biometrics.BiometricPrompt$Builder,int)
com.google.android.gms.internal.measurement.zzng: com.google.android.gms.internal.measurement.zzng[] values()
androidx.core.view.WindowInsetsCompat$Impl: boolean isRound()
androidx.appcompat.widget.ActionBarContextView: ActionBarContextView(android.content.Context,android.util.AttributeSet)
androidx.appcompat.widget.FitWindowsLinearLayout: void setOnFitSystemWindowsListener(androidx.appcompat.widget.FitWindowsViewGroup$OnFitSystemWindowsListener)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getSubtitle()
io.flutter.embedding.engine.FlutterJNI: void nativeRegisterTexture(long,long,java.lang.ref.WeakReference)
androidx.appcompat.widget.SearchView$SearchAutoComplete: SearchView$SearchAutoComplete(android.content.Context,android.util.AttributeSet)
io.flutter.embedding.engine.FlutterJNI: void setPlatformViewsController2(io.flutter.plugin.platform.PlatformViewsController2)
io.flutter.embedding.engine.FlutterJNI: android.view.SurfaceControl$Transaction createTransaction()
androidx.core.view.ViewCompat$Api28Impl: java.lang.CharSequence getAccessibilityPaneTitle(android.view.View)
com.google.firebase.installations.FirebaseInstallationsRegistrar: FirebaseInstallationsRegistrar()
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: void remove()
androidx.biometric.CryptoObjectUtils$Api23Impl: void setBlockModeCBC(android.security.keystore.KeyGenParameterSpec$Builder)
androidx.biometric.CryptoObjectUtils$Api28Impl: java.security.Signature getSignature(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.appcompat.widget.AppCompatButton: int getAutoSizeMaxTextSize()
androidx.appcompat.widget.AppCompatRadioButton: int getCompoundPaddingLeft()
androidx.exifinterface.media.ExifInterfaceUtils$Api23Impl: void setDataSource(android.media.MediaMetadataRetriever,android.media.MediaDataSource)
androidx.appcompat.widget.AppCompatCheckedTextView: void setCustomSelectionActionModeCallback(android.view.ActionMode$Callback)
io.flutter.view.AccessibilityBridge$Action: io.flutter.view.AccessibilityBridge$Action valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageButton: void setImageURI(android.net.Uri)
androidx.appcompat.app.AlertController$RecycleListView: AlertController$RecycleListView(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTint(android.graphics.drawable.Drawable,int)
androidx.biometric.KeyguardUtils$Api23Impl: android.app.KeyguardManager getKeyguardManager(android.content.Context)
androidx.core.view.WindowInsetsCompat$BuilderImpl29: WindowInsetsCompat$BuilderImpl29()
com.google.android.gms.measurement.AppMeasurementJobService: AppMeasurementJobService()
com.google.android.gms.internal.measurement.zzbx: android.os.IBinder asBinder()
androidx.core.view.WindowInsetsCompat$Impl21: androidx.core.graphics.Insets getStableInsets()
androidx.core.graphics.drawable.IconCompat: IconCompat()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundResource(int)
com.google.firebase.sessions.DataCollectionState: com.google.firebase.sessions.DataCollectionState valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatImageView: void setBackgroundResource(int)
androidx.appcompat.widget.ActionMenuView: void setOverflowIcon(android.graphics.drawable.Drawable)
io.flutter.embedding.android.FlutterImageView: io.flutter.embedding.engine.renderer.FlutterRenderer getAttachedRenderer()
androidx.appcompat.widget.AppCompatImageButton: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.biometric.BiometricManager$Api29Impl: java.lang.reflect.Method getCanAuthenticateWithCryptoMethod()
androidx.appcompat.widget.AppCompatSpinner: android.content.Context getPopupContext()
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getSystemWindowInsets()
androidx.core.view.ViewCompat$Api28Impl: void setAutofillId(android.view.View,androidx.core.view.autofill.AutofillIdCompat)
io.flutter.plugin.platform.SingleViewPresentation: SingleViewPresentation(android.content.Context,android.view.Display,io.flutter.plugin.platform.AccessibilityEventsDelegate,io.flutter.plugin.platform.SingleViewPresentation$PresentationState,android.view.View$OnFocusChangeListener,boolean)
androidx.core.widget.NestedScrollView$Api21Impl: boolean getClipToPadding(android.view.ViewGroup)
androidx.core.view.ViewCompat$Api21Impl: boolean startNestedScroll(android.view.View,int)
androidx.lifecycle.EmptyActivityLifecycleCallbacks: void onActivityResumed(android.app.Activity)
androidx.appcompat.widget.Toolbar: void setContentInsetEndWithActions(int)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: AnalyticsConnectorRegistrar()
androidx.appcompat.widget.AppCompatImageView: void setSupportBackgroundTintMode(android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: boolean isConsumed()
androidx.preference.UnPressableLinearLayout: UnPressableLinearLayout(android.content.Context,android.util.AttributeSet)
androidx.appcompat.view.menu.ActionMenuItemView: void setChecked(boolean)
androidx.appcompat.widget.Toolbar: void setNavigationOnClickListener(android.view.View$OnClickListener)
androidx.appcompat.widget.AppCompatCheckBox: void setButtonDrawable(android.graphics.drawable.Drawable)
androidx.core.view.ViewCompat$Api26Impl: void setNextClusterForwardId(android.view.View,int)
androidx.window.extensions.core.util.function.Function: java.lang.Object apply(java.lang.Object)
androidx.biometric.CryptoObjectUtils$Api28Impl: android.hardware.biometrics.BiometricPrompt$CryptoObject create(java.security.Signature)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void generateEventId(com.google.android.gms.internal.measurement.zzdi)
com.google.android.gms.internal.measurement.zzbx: boolean onTransact(int,android.os.Parcel,android.os.Parcel,int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setTintList(android.graphics.drawable.Drawable,android.content.res.ColorStateList)
io.flutter.embedding.engine.FlutterJNI: void nativeDeferredComponentInstallFailure(int,java.lang.String,boolean)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void updateTexImage()
io.flutter.plugins.flutter_plugin_android_lifecycle.FlutterAndroidLifecyclePlugin: FlutterAndroidLifecyclePlugin()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setDataCollectionEnabled(boolean)
com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture: com.google.firebase.crashlytics.internal.common.CommonUtils$Architecture valueOf(java.lang.String)
androidx.appcompat.widget.AppCompatMultiAutoCompleteTextView: android.graphics.PorterDuff$Mode getSupportBackgroundTintMode()
androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke: androidx.datastore.preferences.protobuf.GeneratedMessageLite$MethodToInvoke[] values()
io.flutter.embedding.engine.plugins.lifecycle.HiddenLifecycleReference: HiddenLifecycleReference(androidx.lifecycle.Lifecycle)
androidx.preference.PreferenceGroup: PreferenceGroup(android.content.Context,android.util.AttributeSet)
com.google.firebase.concurrent.UiExecutor: com.google.firebase.concurrent.UiExecutor[] values()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintList(android.view.MenuItem,android.content.res.ColorStateList)
androidx.biometric.BiometricFragment: BiometricFragment()
androidx.core.widget.NestedScrollView: void setSmoothScrollingEnabled(boolean)
androidx.appcompat.widget.AppCompatImageButton: void setImageDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.AppCompatTextView: void setSupportCompoundDrawablesTintList(android.content.res.ColorStateList)
io.flutter.embedding.android.FlutterView$ZeroSides: io.flutter.embedding.android.FlutterView$ZeroSides valueOf(java.lang.String)
androidx.preference.internal.PreferenceImageView: PreferenceImageView(android.content.Context,android.util.AttributeSet)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object getMeasurementApiStatus(kotlin.coroutines.Continuation)
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api34Impl: void setAccessibilityDataSensitive(android.view.accessibility.AccessibilityNodeInfo,boolean)
androidx.fragment.app.FragmentContainerView: androidx.fragment.app.Fragment getFragment()
androidx.core.app.AppOpsManagerCompat$Api23Impl: java.lang.Object getSystemService(android.content.Context,java.lang.Class)
io.flutter.embedding.engine.FlutterJNI: boolean nativeIsSurfaceControlEnabled(long)
androidx.appcompat.widget.AppCompatImageButton: void setSupportBackgroundTintList(android.content.res.ColorStateList)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorView: android.graphics.Matrix getPlatformViewMatrix()
androidx.appcompat.widget.AppCompatButton: android.graphics.PorterDuff$Mode getSupportCompoundDrawablesTintMode()
androidx.activity.OnBackPressedDispatcher$Api34Impl: android.window.OnBackInvokedCallback createOnBackAnimationCallback(kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function1,kotlin.jvm.functions.Function0,kotlin.jvm.functions.Function0)
androidx.core.view.WindowInsetsCompat$Impl: boolean equals(java.lang.Object)
androidx.appcompat.widget.SwitchCompat: void setSwitchMinWidth(int)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$302(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: boolean access$302(io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer,boolean)
com.google.android.gms.measurement.internal.zzip: com.google.android.gms.measurement.internal.zzip[] values()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: void setFillAlpha(float)
androidx.appcompat.view.menu.ActionMenuItemView: ActionMenuItemView(android.content.Context,android.util.AttributeSet)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: void setHotspot(android.graphics.drawable.Drawable,float,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VectorDrawableCompatState: int getChangingConfigurations()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearConditionalUserProperty(java.lang.String,java.lang.String,android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: void onDisplayOverlaySurface(int,int,int,int,int)
androidx.appcompat.widget.Toolbar: void setLogo(int)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: void setRotation(float)
io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness: io.flutter.embedding.engine.systemchannels.PlatformChannel$Brightness valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl30: WindowInsetsCompat$Impl30(androidx.core.view.WindowInsetsCompat,android.view.WindowInsets)
androidx.appcompat.widget.AppCompatTextView: java.lang.CharSequence getText()
androidx.appcompat.widget.AppCompatImageButton: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.core.widget.ImageViewCompat$Api21Impl: android.graphics.PorterDuff$Mode getImageTintMode(android.widget.ImageView)
io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode: io.flutter.embedding.engine.systemchannels.PlatformViewsChannel$PlatformViewCreationRequest$RequestedDisplayMode[] values()
androidx.core.content.ContextCompat$Api21Impl: java.io.File getNoBackupFilesDir(android.content.Context)
androidx.appcompat.widget.Toolbar: void setOnMenuItemClickListener(androidx.appcompat.widget.Toolbar$OnMenuItemClickListener)
io.flutter.embedding.engine.renderer.SurfaceTextureWrapper: void markDirty()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: ReportFragment$LifecycleCallbacks()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VGroup: java.lang.String getGroupName()
androidx.core.view.ViewCompat$Api21Impl: void setOnApplyWindowInsetsListener(android.view.View,androidx.core.view.OnApplyWindowInsetsListener)
io.flutter.embedding.engine.FlutterJNI: void lambda$decodeImage$0(long,android.graphics.ImageDecoder,android.graphics.ImageDecoder$ImageInfo,android.graphics.ImageDecoder$Source)
androidx.core.view.ViewCompat$Api26Impl: android.view.autofill.AutofillId getAutofillId(android.view.View)
io.flutter.view.AccessibilityViewEmbedder: void cacheVirtualIdMappings(android.view.View,int,int)
androidx.core.view.ViewCompat$Api26Impl: boolean isFocusedByDefault(android.view.View)
io.flutter.embedding.android.KeyData$Type: io.flutter.embedding.android.KeyData$Type valueOf(java.lang.String)
androidx.core.content.ContextCompat$Api24Impl: java.io.File getDataDir(android.content.Context)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization[] values()
androidx.core.view.ViewParentCompat$Api21Impl: boolean onNestedPreFling(android.view.ViewParent,android.view.View,float,float)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathEnd()
io.flutter.plugin.platform.PlatformViewWrapper: void setTouchProcessor(io.flutter.embedding.android.AndroidTouchProcessor)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setThreshold(int)
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiMode[] values()
androidx.core.view.accessibility.AccessibilityNodeInfoCompat$Api33Impl: androidx.core.view.accessibility.AccessibilityNodeInfoCompat getChild(android.view.accessibility.AccessibilityNodeInfo,int,int)
androidx.core.view.WindowInsetsCompat$Impl: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
androidx.appcompat.widget.AppCompatAutoCompleteTextView: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: android.content.res.ColorStateList getTrackTintList()
com.google.android.gms.dynamite.DynamiteModule$DynamiteLoaderClassLoader: DynamiteModule$DynamiteLoaderClassLoader()
androidx.core.view.ViewCompat$Api26Impl: void setFocusedByDefault(android.view.View,boolean)
com.google.android.gms.measurement.AppMeasurement: java.lang.String getGmpAppId()
io.flutter.view.AccessibilityViewEmbedder: boolean onAccessibilityHoverEvent(int,android.view.MotionEvent)
androidx.appcompat.widget.Toolbar: void setLogo(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SearchView: java.lang.CharSequence getQueryHint()
com.google.firebase.sessions.FirebaseSessionsRegistrar: com.google.firebase.sessions.SessionFirelogPublisher getComponents$lambda-2(com.google.firebase.components.ComponentContainer)
androidx.appcompat.widget.ViewStubCompat: int getLayoutResource()
io.flutter.plugins.imagepicker.Messages$SourceCamera: io.flutter.plugins.imagepicker.Messages$SourceCamera[] values()
androidx.core.widget.TextViewCompat$Api23Impl: int getBreakStrategy(android.widget.TextView)
androidx.appcompat.view.menu.ListMenuItemView: androidx.appcompat.view.menu.MenuItemImpl getItemData()
androidx.recyclerview.widget.RecyclerView: androidx.recyclerview.widget.RecyclerView$OnFlingListener getOnFlingListener()
androidx.appcompat.widget.LinearLayoutCompat: int getDividerWidth()
android.support.v4.graphics.drawable.IconCompatParcelizer: androidx.core.graphics.drawable.IconCompat read(androidx.versionedparcelable.VersionedParcel)
androidx.core.view.WindowInsetsCompat$Impl20: androidx.core.graphics.Insets getVisibleInsets(android.view.View)
androidx.appcompat.widget.AppCompatEditText: void setTextClassifier(android.view.textclassifier.TextClassifier)
androidx.browser.customtabs.CustomTabsIntent$Api34Impl: void setShareIdentityEnabled(android.app.ActivityOptions,boolean)
androidx.appcompat.widget.AppCompatTextView: android.view.textclassifier.TextClassifier getTextClassifier()
io.flutter.embedding.engine.FlutterJNI: void dispatchSemanticsAction(int,io.flutter.view.AccessibilityBridge$Action)
androidx.appcompat.widget.Toolbar: void setTitle(java.lang.CharSequence)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextCapitalization valueOf(java.lang.String)
com.google.firebase.installations.FirebaseInstallationsRegistrar: com.google.firebase.installations.FirebaseInstallationsApi lambda$getComponents$0(com.google.firebase.components.ComponentContainer)
androidx.biometric.KeyguardUtils$Api16Impl: boolean isKeyguardSecure(android.app.KeyguardManager)
androidx.core.view.ViewCompat$Api21Impl: void setTransitionName(android.view.View,java.lang.String)
androidx.core.app.NotificationManagerCompat$Api24Impl: int getImportance(android.app.NotificationManager)
androidx.window.area.reflectionguard.WindowAreaComponentApi3Requirements: void addRearDisplayPresentationStatusListener(androidx.window.extensions.core.util.function.Consumer)
androidx.biometric.BiometricViewModel: BiometricViewModel()
io.flutter.plugins.localauth.LocalAuthPlugin: LocalAuthPlugin()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPaused(android.app.Activity)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void logHealthData(int,java.lang.String,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper,com.google.android.gms.dynamic.IObjectWrapper)
androidx.core.widget.TextViewCompat$Api23Impl: void setCompoundDrawableTintMode(android.widget.TextView,android.graphics.PorterDuff$Mode)
androidx.core.view.WindowInsetsCompat$Impl: androidx.core.graphics.Insets getMandatorySystemGestureInsets()
com.google.firebase.analytics.FirebaseAnalytics$ConsentType: com.google.firebase.analytics.FirebaseAnalytics$ConsentType valueOf(java.lang.String)
com.google.android.gms.common.api.internal.LifecycleCallback: com.google.android.gms.common.api.internal.LifecycleFragment getChimeraLifecycleFragmentImpl(com.google.android.gms.common.api.internal.LifecycleActivity)
io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar: java.util.List getComponents()
io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness: io.flutter.embedding.engine.systemchannels.SettingsChannel$PlatformBrightness valueOf(java.lang.String)
androidx.core.widget.EdgeEffectCompat$Api31Impl: float onPullDistance(android.widget.EdgeEffect,float,float)
kotlinx.coroutines.selects.TrySelectDetailedResult: kotlinx.coroutines.selects.TrySelectDetailedResult valueOf(java.lang.String)
androidx.profileinstaller.ProfileInstallerInitializer$Handler28Impl: android.os.Handler createAsync(android.os.Looper)
androidx.fragment.app.FragmentContainerView: void setOnApplyWindowInsetsListener(android.view.View$OnApplyWindowInsetsListener)
androidx.appcompat.widget.AppCompatCheckBox: void setBackgroundDrawable(android.graphics.drawable.Drawable)
androidx.appcompat.widget.SwitchCompat: void setTextOn(java.lang.CharSequence)
androidx.appcompat.widget.Toolbar: void setLogoDescription(int)
androidx.core.widget.NestedScrollView: int getMaxScrollAmount()
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getStrokeAlpha()
io.flutter.plugins.GeneratedPluginRegistrant: void registerWith(io.flutter.embedding.engine.FlutterEngine)
io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback: boolean access$102(io.flutter.plugin.editing.ImeSyncDeferringInsetsCallback,boolean)
androidx.core.view.WindowInsetsCompat$Impl20: void copyWindowDataInto(androidx.core.view.WindowInsetsCompat)
com.google.firebase.installations.FirebaseInstallationsException$Status: com.google.firebase.installations.FirebaseInstallationsException$Status valueOf(java.lang.String)
androidx.window.area.reflectionguard.ExtensionWindowAreaStatusRequirements: android.util.DisplayMetrics getWindowAreaDisplayMetrics()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setConsent(android.os.Bundle,long)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getSessionId(com.google.android.gms.internal.measurement.zzdi)
io.flutter.embedding.engine.FlutterJNI: void onEndFrame()
io.flutter.view.AccessibilityBridge$Flag: io.flutter.view.AccessibilityBridge$Flag valueOf(java.lang.String)
io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType: io.flutter.embedding.engine.systemchannels.TextInputChannel$TextInputType valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageTextureRegistryEntry: void maybeWaitOnFence(android.media.Image)
androidx.vectordrawable.graphics.drawable.VectorDrawableCompat$VFullPath: float getTrimPathOffset()
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setContentDescription(android.view.MenuItem,java.lang.CharSequence)
androidx.core.view.ViewGroupCompat$Api21Impl: int getNestedScrollAxes(android.view.ViewGroup)
androidx.core.view.WindowInsetsCompat$BuilderImpl: void setMandatorySystemGestureInsets(androidx.core.graphics.Insets)
io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState: io.flutter.embedding.engine.systemchannels.LifecycleChannel$AppLifecycleState valueOf(java.lang.String)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: int numImages()
io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay: io.flutter.embedding.engine.systemchannels.PlatformChannel$SystemUiOverlay[] values()
io.flutter.view.AccessibilityBridge$TextDirection: io.flutter.view.AccessibilityBridge$TextDirection valueOf(java.lang.String)
kotlinx.coroutines.android.AndroidExceptionPreHandler: AndroidExceptionPreHandler()
androidx.core.view.ViewCompat$Api29Impl: void saveAttributeDataForStyleable(android.view.View,android.content.Context,int[],android.util.AttributeSet,android.content.res.TypedArray,int,int)
androidx.core.graphics.drawable.DrawableCompat$Api21Impl: android.graphics.ColorFilter getColorFilter(android.graphics.drawable.Drawable)
androidx.privacysandbox.ads.adservices.measurement.MeasurementManager$Api33Ext5Impl: java.lang.Object registerSource(android.net.Uri,android.view.InputEvent,kotlin.coroutines.Continuation)
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseCrashlyticsPlugin: FlutterFirebaseCrashlyticsPlugin()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void registerIn(android.app.Activity)
com.google.android.datatransport.cct.internal.ClientInfo$ClientType: com.google.android.datatransport.cct.internal.ClientInfo$ClientType[] values()
io.flutter.plugins.pathprovider.Messages$StorageDirectory: io.flutter.plugins.pathprovider.Messages$StorageDirectory[] values()
androidx.biometric.CryptoObjectUtils$Api28Impl: javax.crypto.Cipher getCipher(android.hardware.biometrics.BiometricPrompt$CryptoObject)
androidx.core.view.MenuItemCompat$Api26Impl: android.view.MenuItem setIconTintMode(android.view.MenuItem,android.graphics.PorterDuff$Mode)
androidx.core.graphics.drawable.IconCompat$Api28Impl: android.net.Uri getUri(java.lang.Object)
io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType: io.flutter.embedding.engine.systemchannels.PlatformChannel$HapticFeedbackType valueOf(java.lang.String)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void setEventInterceptor(com.google.android.gms.internal.measurement.zzdj)
androidx.core.hardware.fingerprint.FingerprintManagerCompat$Api23Impl: android.hardware.fingerprint.FingerprintManager$CryptoObject getCryptoObject(java.lang.Object)
androidx.core.widget.CompoundButtonCompat$Api21Impl: void setButtonTintList(android.widget.CompoundButton,android.content.res.ColorStateList)
androidx.appcompat.widget.Toolbar: int getContentInsetStartWithNavigation()
androidx.core.view.ViewConfigurationCompat$Api34Impl: int getScaledMinimumFlingVelocity(android.view.ViewConfiguration,int,int,int)
androidx.window.area.reflectionguard.WindowAreaComponentApi2Requirements: void endRearDisplaySession()
androidx.biometric.CryptoObjectUtils$Api23Impl: android.security.keystore.KeyGenParameterSpec buildKeyGenParameterSpec(android.security.keystore.KeyGenParameterSpec$Builder)
androidx.biometric.BiometricManager$Api30Impl: int canAuthenticate(android.hardware.biometrics.BiometricManager,int)
androidx.datastore.preferences.protobuf.JavaType: androidx.datastore.preferences.protobuf.JavaType valueOf(java.lang.String)
androidx.appcompat.widget.Toolbar: android.graphics.drawable.Drawable getOverflowIcon()
io.flutter.view.AccessibilityViewEmbedder: java.lang.Integer getRecordFlutterId(android.view.View,android.view.accessibility.AccessibilityRecord)
androidx.activity.Api34Impl: android.window.BackEvent createOnBackEvent(float,float,float,int)
com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar: java.util.List getComponents()
androidx.appcompat.widget.ActionBarOverlayLayout: void setActionBarVisibilityCallback(androidx.appcompat.widget.ActionBarOverlayLayout$ActionBarVisibilityCallback)
io.flutter.view.TextureRegistry$GLTextureConsumer: android.graphics.SurfaceTexture getSurfaceTexture()
androidx.appcompat.widget.ContentFrameLayout: android.util.TypedValue getMinWidthMinor()
io.flutter.plugin.platform.SingleViewPresentation: void onCreate(android.os.Bundle)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI spawn(java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getTrackDrawable()
androidx.lifecycle.ReportFragment$LifecycleCallbacks: void onActivityPostStarted(android.app.Activity)
androidx.appcompat.widget.ContentFrameLayout: void setAttachListener(androidx.appcompat.widget.ContentFrameLayout$OnAttachListener)
io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer: io.flutter.embedding.engine.renderer.FlutterRenderer$ImageReaderSurfaceProducer$PerImageReader getOrCreatePerImageReader(android.media.ImageReader)
androidx.appcompat.widget.Toolbar: void setSubtitle(int)
androidx.core.view.WindowInsetsCompat$BuilderImpl20: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.widget.Toolbar: void setTitleTextColor(int)
androidx.appcompat.widget.SearchView$SearchAutoComplete: void setSearchView(androidx.appcompat.widget.SearchView)
io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode: io.flutter.embedding.android.FlutterActivityLaunchConfigs$BackgroundMode[] values()
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType valueOf(java.lang.String)
io.flutter.embedding.engine.FlutterJNI: io.flutter.embedding.engine.FlutterJNI nativeSpawn(long,java.lang.String,java.lang.String,java.lang.String,java.util.List,long)
io.flutter.view.FlutterCallbackInformation: io.flutter.view.FlutterCallbackInformation lookupCallbackInformation(long)
io.flutter.embedding.engine.FlutterOverlaySurface: FlutterOverlaySurface(int,android.view.Surface)
androidx.datastore.preferences.protobuf.FieldType$Collection: androidx.datastore.preferences.protobuf.FieldType$Collection[] values()
io.flutter.view.FlutterCallbackInformation: FlutterCallbackInformation(java.lang.String,java.lang.String,java.lang.String)
io.flutter.embedding.engine.FlutterJNI: void init(android.content.Context,java.lang.String[],java.lang.String,java.lang.String,java.lang.String,long,int)
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus valueOf(java.lang.String)
androidx.core.app.AppOpsManagerCompat$Api29Impl: android.app.AppOpsManager getSystemService(android.content.Context)
kotlinx.coroutines.android.AndroidDispatcherFactory: kotlinx.coroutines.MainCoroutineDispatcher createDispatcher(java.util.List)
io.flutter.plugins.imagepicker.Messages$SourceType: io.flutter.plugins.imagepicker.Messages$SourceType[] values()
com.google.firebase.installations.remote.TokenResult$ResponseCode: com.google.firebase.installations.remote.TokenResult$ResponseCode[] values()
io.flutter.embedding.engine.FlutterJNI: void handlePlatformMessageResponse(int,java.nio.ByteBuffer)
androidx.core.widget.NestedScrollView: void setOnScrollChangeListener(androidx.core.widget.NestedScrollView$OnScrollChangeListener)
io.flutter.plugins.localauth.Messages$AuthResult: io.flutter.plugins.localauth.Messages$AuthResult valueOf(java.lang.String)
androidx.core.view.WindowInsetsCompat$Impl20: void setRootWindowInsets(androidx.core.view.WindowInsetsCompat)
io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack: void pushTransform(float[])
androidx.biometric.BiometricFragment$Api29Impl: void setConfirmationRequired(android.hardware.biometrics.BiometricPrompt$Builder,boolean)
io.flutter.embedding.engine.FlutterJNI: void onVsync(long,long,long)
com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag: com.google.android.datatransport.runtime.scheduling.jobscheduling.SchedulerConfig$Flag valueOf(java.lang.String)
androidx.lifecycle.Lifecycle$Event: androidx.lifecycle.Lifecycle$Event[] values()
io.flutter.embedding.engine.FlutterJNI: void nativeCleanupMessageData(long)
io.flutter.view.TextureRegistry$SurfaceProducer: int getHeight()
androidx.appcompat.widget.ActionBarOverlayLayout: void setHasNonEmbeddedTabs(boolean)
androidx.startup.InitializationProvider: InitializationProvider()
androidx.appcompat.widget.AppCompatTextView: android.content.res.ColorStateList getSupportBackgroundTintList()
androidx.appcompat.widget.LinearLayoutCompat: void setOrientation(int)
androidx.core.view.ViewCompat$Api20Impl: android.view.WindowInsets dispatchApplyWindowInsets(android.view.View,android.view.WindowInsets)
net.nfet.flutter.printing.PrintingPlugin: PrintingPlugin()
io.flutter.view.TextureRegistry$SurfaceTextureEntry: android.graphics.SurfaceTexture surfaceTexture()
androidx.appcompat.widget.SwitchCompat: android.graphics.drawable.Drawable getThumbDrawable()
com.google.android.gms.common.GooglePlayServicesMissingManifestValueException: GooglePlayServicesMissingManifestValueException()
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void clearMeasurementEnabled(long)
androidx.lifecycle.LifecycleDispatcher$DispatcherActivityCallback: LifecycleDispatcher$DispatcherActivityCallback()
com.google.firebase.datatransport.TransportRegistrar: TransportRegistrar()
androidx.window.layout.adapter.sidecar.SidecarCompat$TranslatingCallback: void onDeviceStateChanged(androidx.window.sidecar.SidecarDeviceState)
androidx.lifecycle.ProcessLifecycleOwner$attach$1: void onActivityPaused(android.app.Activity)
androidx.profileinstaller.ProfileInstallReceiver: ProfileInstallReceiver()
io.flutter.embedding.engine.FlutterJNI: void nativePrefetchDefaultFontManager()
io.flutter.embedding.engine.FlutterJNI: float getScaledFontSize(float,int)
com.google.android.gms.measurement.internal.AppMeasurementDynamiteService: void getMaxUserProperties(java.lang.String,com.google.android.gms.internal.measurement.zzdi)
com.google.android.gms.internal.measurement.zzft$zzk$zzb: com.google.android.gms.internal.measurement.zzft$zzk$zzb[] values()
io.flutter.embedding.engine.FlutterJNI: void onFirstFrame()
androidx.appcompat.widget.ActionBarContextView: void setCustomView(android.view.View)
io.flutter.plugins.localauth.Messages$AuthClassification: io.flutter.plugins.localauth.Messages$AuthClassification[] values()
io.flutter.view.AccessibilityViewEmbedder: boolean performAction(int,int,android.os.Bundle)
io.flutter.view.TextureRegistry$SurfaceProducer: void setSize(int,int)
androidx.appcompat.widget.Toolbar: java.lang.CharSequence getCollapseContentDescription()
com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus: com.google.firebase.installations.local.PersistedInstallation$RegistrationStatus[] values()
androidx.core.widget.NestedScrollView: float getVerticalScrollFactorCompat()
androidx.core.view.WindowInsetsCompat$BuilderImpl29: void setSystemWindowInsets(androidx.core.graphics.Insets)
androidx.appcompat.view.menu.ActionMenuItemView: void setItemInvoker(androidx.appcompat.view.menu.MenuBuilder$ItemInvoker)
androidx.tracing.TraceApi29Impl: boolean isEnabled()
androidx.appcompat.widget.SearchView: void setSearchableInfo(android.app.SearchableInfo)
androidx.core.app.CoreComponentFactory: CoreComponentFactory()
dev.fluttercommunity.plus.share.SharePlusPendingIntent: SharePlusPendingIntent()
androidx.privacysandbox.ads.adservices.java.measurement.MeasurementManagerFutures$Api33Ext5JavaImpl: com.google.common.util.concurrent.ListenableFuture registerTriggerAsync(android.net.Uri)
androidx.appcompat.widget.AppCompatSpinner: void setPopupBackgroundResource(int)
io.flutter.embedding.engine.FlutterJNI: void onDisplayPlatformView(int,int,int,int,int,int,int,io.flutter.embedding.engine.mutatorsstack.FlutterMutatorsStack)
androidx.preference.CheckBoxPreference: CheckBoxPreference(android.content.Context,android.util.AttributeSet)
io.flutter.plugins.firebase.crashlytics.FlutterFirebaseAppRegistrar: FlutterFirebaseAppRegistrar()
androidx.core.view.WindowInsetsCompat$BuilderImpl20: WindowInsetsCompat$BuilderImpl20()
io.flutter.embedding.android.FlutterTextureView: void setRenderSurface(android.view.Surface)
io.flutter.embedding.android.KeyData$DeviceType: io.flutter.embedding.android.KeyData$DeviceType[] values()
androidx.appcompat.widget.MenuPopupWindow$MenuDropDownListView: void setHoverListener(androidx.appcompat.widget.MenuItemHoverListener)
androidx.appcompat.view.menu.ListMenuItemView: void setForceShowIcon(boolean)
