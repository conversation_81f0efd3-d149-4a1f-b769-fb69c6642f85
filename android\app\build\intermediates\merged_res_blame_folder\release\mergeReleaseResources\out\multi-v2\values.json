{"logs": [{"outputFile": "com.example.wargani.app-mergeReleaseResources-45:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "427", "startColumns": "4", "startOffsets": "24667", "endColumns": "82", "endOffsets": "24745"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d4e1de4e870e893108c546e2600c23f\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "396,435", "startColumns": "4,4", "startOffsets": "22537,25261", "endColumns": "67,166", "endOffsets": "22600,25423"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\185f2479ab24942c0bba65b9ff947d79\\transformed\\jetified-appcompat-resources-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2274,2290,2296,3584,3600", "startColumns": "4,4,4,4,4", "startOffsets": "143584,144009,144187,188882,189293", "endLines": "2289,2295,2305,3599,3603", "endColumns": "24,24,24,24,24", "endOffsets": "144004,144182,144466,189288,189415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cceca150324ee75eadce8003b387b1fb\\transformed\\biometric-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,110,264,436,439,444,445,446,447,448,449,450,452,453,454", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4363,14768,25428,25610,25942,26031,26130,26238,26335,26423,26523,26675,26772,26882", "endLines": "5,7,10,14,33,110,264,436,439,444,445,446,447,448,449,450,452,453,454", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4414,14816,25499,25665,26026,26125,26233,26330,26418,26518,26588,26767,26877,26966"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,346,2253,2259,3545,3553,3568", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,19941,142714,142909,187332,187614,188228", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,346,2258,2263,3552,3567,3583", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,19996,142904,143062,187609,188223,188877"}}, {"source": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\local_auth_android-1.0.49\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "111,134,272,276,471", "startColumns": "4,4,4,4,4", "startOffsets": "4419,6031,15432,15701,28184", "endLines": "111,134,272,276,476", "endColumns": "44,43,45,47,10", "endOffsets": "4459,6070,15473,15744,28479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "390", "startColumns": "4", "startOffsets": "22180", "endColumns": "49", "endOffsets": "22225"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "96,151,292,293,294,295,296,297,298,355,356,357,397,398,437,440,459,460,466,467,468,1544,1728,1731,1737,1743,1746,1752,1756,1759,1766,1772,1775,1781,1786,1791,1798,1800,1806,1812,1820,1825,1832,1837,1843,1847,1854,1858,1864,1870,1873,1877,1878,2792,2807,2946,2984,3126,3301,3319,3383,3393,3403,3410,3416,3520,3670,3687", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3310,7014,16727,16791,16846,16914,16981,17046,17103,20358,20406,20454,22605,22668,25504,25670,27403,27447,27779,27918,27968,96291,110029,110134,110379,110717,110863,111203,111415,111578,111985,112323,112446,112785,113024,113281,113652,113712,114050,114336,114785,115077,115465,115770,116114,116359,116689,116896,117164,117437,117581,117782,117829,160243,160766,167552,168853,173795,179390,180018,181943,182225,182530,182792,183052,186568,192446,192976", "endLines": "96,151,292,293,294,295,296,297,298,355,356,357,397,398,437,440,459,462,466,467,468,1560,1730,1736,1742,1745,1751,1755,1758,1765,1771,1774,1780,1785,1790,1797,1799,1805,1811,1819,1824,1831,1836,1842,1846,1853,1857,1863,1869,1872,1876,1877,1878,2796,2817,2965,2987,3135,3308,3382,3392,3402,3409,3415,3458,3532,3686,3703", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3378,7078,16786,16841,16909,16976,17041,17098,17155,20401,20449,20510,22663,22726,25537,25722,27442,27582,27913,27963,28011,97724,110129,110374,110712,110858,111198,111410,111573,111980,112318,112441,112780,113019,113276,113647,113707,114045,114331,114780,115072,115460,115765,116109,116354,116684,116891,117159,117432,117576,117777,117824,117880,160423,161162,168276,168997,174122,179633,181938,182220,182525,182787,183047,184470,187015,192971,193539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93eeca70efd8419049cd49df8af72af1\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "360,388", "startColumns": "4,4", "startOffsets": "20611,22066", "endColumns": "41,59", "endOffsets": "20648,22121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "347,363,391,3042,3047", "startColumns": "4,4,4,4,4", "startOffsets": "20001,20755,22230,171414,171584", "endLines": "347,363,391,3046,3050", "endColumns": "56,64,63,24,24", "endOffsets": "20053,20815,22289,171579,171728"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "348,349,354,361,362,381,382,383,384,385", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "20058,20098,20315,20653,20708,21725,21779,21831,21880,21941", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "20093,20140,20353,20703,20750,21774,21826,21875,21936,21986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "389", "startColumns": "4", "startOffsets": "22126", "endColumns": "53", "endOffsets": "22175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,104,105,124,125,149,150,252,253,254,255,256,257,258,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,352,353,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,399,428,429,430,431,432,433,434,465,1990,1991,1995,1996,2000,2144,2145,2801,2818,2988,3021,3051,3084", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,3884,3956,5354,5419,6882,6951,13944,14014,14082,14154,14224,14285,14359,15749,15810,15871,15933,15997,16059,16120,16188,16288,16348,16414,16487,16556,16613,16665,17613,17685,17761,17826,17885,17944,18004,18064,18124,18184,18244,18304,18364,18424,18484,18544,18603,18663,18723,18783,18843,18903,18963,19023,19083,19143,19203,19262,19322,19382,19441,19500,19559,19618,19677,20245,20280,20866,20921,20984,21039,21097,21155,21216,21279,21336,21387,21437,21498,21555,21621,21655,21690,22731,24750,24817,24889,24958,25027,25101,25173,27708,126854,126971,127172,127282,127483,138907,138979,160563,161167,169002,170733,171733,172415", "endLines": "61,104,105,124,125,149,150,252,253,254,255,256,257,258,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,352,353,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,399,428,429,430,431,432,433,434,465,1990,1994,1995,1999,2000,2144,2145,2806,2827,3020,3041,3083,3089", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,3951,4039,5414,5480,6946,7009,14009,14077,14149,14219,14280,14354,14427,15805,15866,15928,15992,16054,16115,16183,16283,16343,16409,16482,16551,16608,16660,16722,17680,17756,17821,17880,17939,17999,18059,18119,18179,18239,18299,18359,18419,18479,18539,18598,18658,18718,18778,18838,18898,18958,19018,19078,19138,19198,19257,19317,19377,19436,19495,19554,19613,19672,19731,20275,20310,20916,20979,21034,21092,21150,21211,21274,21331,21382,21432,21493,21550,21616,21650,21685,21720,22796,24812,24884,24953,25022,25096,25168,25256,27774,126966,127167,127277,127478,127607,138974,139041,160761,161463,170728,171409,172410,172577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "118,119,120,121,250,251,438,441,442,443", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4990,5048,5114,5177,13801,13872,25542,25727,25794,25873", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "5043,5109,5172,5234,13867,13939,25605,25789,25868,25937"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a295c1332cd792405fffabf7b4bbac54\\transformed\\appcompat-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,219,220,224,228,232,237,243,250,254,258,263,267,271,275,279,283,287,293,297,303,307,313,317,322,326,329,333,339,343,349,353,359,362,366,370,374,378,382,383,384,385,388,391,394,397,401,402,403,404,405,408,410,412,414,419,420,424,430,434,435,437,448,449,453,459,463,464,465,469,496,500,501,505,533,703,729,899,925,956,964,970,984,1006,1011,1016,1026,1035,1044,1048,1055,1063,1070,1071,1080,1083,1086,1090,1094,1098,1101,1102,1107,1112,1122,1127,1134,1140,1141,1144,1148,1153,1155,1157,1160,1163,1165,1169,1172,1179,1182,1185,1189,1191,1195,1197,1199,1201,1205,1213,1221,1233,1239,1248,1251,1262,1265,1266,1271,1272,1277,1346,1416,1417,1427,1436,1437,1439,1443,1446,1449,1452,1455,1458,1461,1464,1468,1471,1474,1477,1481,1484,1488,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1514,1516,1517,1518,1519,1520,1521,1522,1523,1525,1526,1528,1529,1531,1533,1534,1536,1537,1538,1539,1540,1541,1543,1544,1545,1546,1547,1548,1550,1552,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1568,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,535,605,666,741,817,894,972,1057,1139,1215,1291,1368,1446,1552,1658,1737,1817,1874,1932,2006,2081,2146,2212,2272,2333,2405,2478,2545,2613,2672,2731,2790,2849,2908,2962,3016,3069,3123,3177,3231,3285,3359,3438,3511,3585,3656,3728,3800,3873,3930,3988,4061,4135,4209,4284,4356,4429,4499,4570,4630,4691,4760,4829,4899,4973,5049,5113,5190,5266,5343,5408,5477,5554,5629,5698,5766,5843,5909,5970,6067,6132,6201,6300,6371,6430,6488,6545,6604,6668,6739,6811,6883,6955,7027,7094,7162,7230,7289,7352,7416,7506,7597,7657,7723,7790,7856,7926,7990,8043,8110,8171,8238,8351,8409,8472,8537,8602,8677,8750,8822,8871,8932,8993,9054,9116,9180,9244,9308,9373,9436,9496,9557,9623,9682,9742,9804,9875,9935,10003,10089,10176,10266,10353,10441,10523,10606,10696,10787,10839,10897,10942,11008,11072,11129,11186,11240,11297,11345,11394,11445,11479,11526,11575,11621,11653,11717,11779,11839,11896,11970,12040,12118,12172,12242,12327,12375,12421,12482,12545,12611,12675,12746,12809,12874,12938,12999,13060,13112,13185,13259,13328,13403,13477,13551,13692,13762,13815,13893,13983,14071,14167,14257,14839,14928,15175,15456,15708,15993,16386,16863,17085,17307,17583,17810,18040,18270,18500,18730,18957,19376,19602,20027,20257,20685,20904,21187,21395,21526,21753,22179,22404,22831,23052,23477,23597,23873,24174,24498,24789,25103,25240,25371,25476,25718,25885,26089,26297,26568,26680,26792,26897,27014,27228,27374,27514,27600,27948,28036,28282,28700,28949,29031,29129,29746,29846,30098,30522,30777,30871,30960,31197,33249,33491,33593,33846,36030,47063,48579,59710,61238,62995,63621,64041,65102,66367,66623,66859,67406,67900,68505,68703,69283,69847,70222,70340,70878,71035,71231,71504,71760,71930,72071,72135,72500,72867,73543,73807,74145,74498,74592,74778,75084,75346,75471,75598,75837,76048,76167,76360,76537,76992,77173,77295,77554,77667,77854,77956,78063,78192,78467,78975,79471,80348,80642,81212,81361,82093,82265,82349,82685,82777,83055,88464,94016,94078,94708,95322,95413,95526,95755,95915,96067,96238,96404,96573,96740,96903,97146,97316,97489,97660,97934,98133,98338,98668,98752,98848,98944,99042,99142,99244,99346,99448,99550,99652,99752,99848,99960,100089,100212,100343,100474,100572,100686,100780,100920,101054,101150,101262,101362,101478,101574,101686,101786,101926,102062,102226,102356,102514,102664,102805,102949,103084,103196,103346,103474,103602,103738,103870,104000,104130,104242,104382,104528,104672,104810,104876,104966,105042,105146,105236,105338,105446,105554,105654,105734,105826,105924,106034,106086,106164,106270,106362,106466,106576,106698,106861,107018,107098,107198,107288,107398,107488,107729,107823,107929,108021,108121,108233,108347,108463,108579,108673,108787,108899,109001,109121,109243,109325,109429,109549,109675,109773,109867,109955,110067,110183,110305,110417,110592,110708,110794,110886,110998,111122,111189,111315,111383,111511,111655,111783,111852,111947,112062,112175,112274,112383,112494,112605,112706,112811,112911,113041,113132,113255,113349,113461,113547,113651,113747,113835,113953,114057,114161,114287,114375,114483,114583,114673,114783,114867,114969,115053,115107,115171,115277,115363,115473,115557,115677,120821,120939,121054,121186,121901,122593,123110,124709,126242,126630,131365,151627,151887,153397,154430,156443,156705,157061,157891,164673,165807,166101,166324,166651,168701,169349,173200,174402,178481,179696,181105", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,702,728,898,924,955,963,969,983,1005,1010,1015,1025,1034,1043,1047,1054,1062,1069,1070,1079,1082,1085,1089,1093,1097,1100,1101,1106,1111,1121,1126,1133,1139,1140,1143,1147,1152,1154,1156,1159,1162,1164,1168,1171,1178,1181,1184,1188,1190,1194,1196,1198,1200,1204,1212,1220,1232,1238,1247,1250,1261,1264,1265,1270,1271,1276,1345,1415,1416,1426,1435,1436,1438,1442,1445,1448,1451,1454,1457,1460,1463,1467,1470,1473,1476,1480,1483,1487,1491,1492,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1513,1515,1516,1517,1518,1519,1520,1521,1522,1524,1525,1527,1528,1530,1532,1533,1535,1536,1537,1538,1539,1540,1542,1543,1544,1545,1546,1547,1549,1551,1553,1554,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1567,1568,1569,1570,1571,1572,1573,1575,1579,1583,1584,1585,1586,1587,1588,1592,1593,1594,1595,1597,1599,1601,1603,1605,1606,1607,1608,1610,1612,1614,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1628,1629,1630,1631,1633,1635,1636,1638,1639,1641,1643,1645,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1658,1659,1660,1661,1663,1664,1665,1666,1667,1669,1671,1673,1675,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1773,1776,1779,1782,1796,1807,1817,1847,1874,1883,1958,2355,2360,2388,2406,2442,2448,2454,2477,2618,2638,2644,2648,2654,2691,2703,2769,2793,2862,2881,2907,2916", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,530,600,661,736,812,889,967,1052,1134,1210,1286,1363,1441,1547,1653,1732,1812,1869,1927,2001,2076,2141,2207,2267,2328,2400,2473,2540,2608,2667,2726,2785,2844,2903,2957,3011,3064,3118,3172,3226,3280,3354,3433,3506,3580,3651,3723,3795,3868,3925,3983,4056,4130,4204,4279,4351,4424,4494,4565,4625,4686,4755,4824,4894,4968,5044,5108,5185,5261,5338,5403,5472,5549,5624,5693,5761,5838,5904,5965,6062,6127,6196,6295,6366,6425,6483,6540,6599,6663,6734,6806,6878,6950,7022,7089,7157,7225,7284,7347,7411,7501,7592,7652,7718,7785,7851,7921,7985,8038,8105,8166,8233,8346,8404,8467,8532,8597,8672,8745,8817,8866,8927,8988,9049,9111,9175,9239,9303,9368,9431,9491,9552,9618,9677,9737,9799,9870,9930,9998,10084,10171,10261,10348,10436,10518,10601,10691,10782,10834,10892,10937,11003,11067,11124,11181,11235,11292,11340,11389,11440,11474,11521,11570,11616,11648,11712,11774,11834,11891,11965,12035,12113,12167,12237,12322,12370,12416,12477,12540,12606,12670,12741,12804,12869,12933,12994,13055,13107,13180,13254,13323,13398,13472,13546,13687,13757,13810,13888,13978,14066,14162,14252,14834,14923,15170,15451,15703,15988,16381,16858,17080,17302,17578,17805,18035,18265,18495,18725,18952,19371,19597,20022,20252,20680,20899,21182,21390,21521,21748,22174,22399,22826,23047,23472,23592,23868,24169,24493,24784,25098,25235,25366,25471,25713,25880,26084,26292,26563,26675,26787,26892,27009,27223,27369,27509,27595,27943,28031,28277,28695,28944,29026,29124,29741,29841,30093,30517,30772,30866,30955,31192,33244,33486,33588,33841,36025,47058,48574,59705,61233,62990,63616,64036,65097,66362,66618,66854,67401,67895,68500,68698,69278,69842,70217,70335,70873,71030,71226,71499,71755,71925,72066,72130,72495,72862,73538,73802,74140,74493,74587,74773,75079,75341,75466,75593,75832,76043,76162,76355,76532,76987,77168,77290,77549,77662,77849,77951,78058,78187,78462,78970,79466,80343,80637,81207,81356,82088,82260,82344,82680,82772,83050,88459,94011,94073,94703,95317,95408,95521,95750,95910,96062,96233,96399,96568,96735,96898,97141,97311,97484,97655,97929,98128,98333,98663,98747,98843,98939,99037,99137,99239,99341,99443,99545,99647,99747,99843,99955,100084,100207,100338,100469,100567,100681,100775,100915,101049,101145,101257,101357,101473,101569,101681,101781,101921,102057,102221,102351,102509,102659,102800,102944,103079,103191,103341,103469,103597,103733,103865,103995,104125,104237,104377,104523,104667,104805,104871,104961,105037,105141,105231,105333,105441,105549,105649,105729,105821,105919,106029,106081,106159,106265,106357,106461,106571,106693,106856,107013,107093,107193,107283,107393,107483,107724,107818,107924,108016,108116,108228,108342,108458,108574,108668,108782,108894,108996,109116,109238,109320,109424,109544,109670,109768,109862,109950,110062,110178,110300,110412,110587,110703,110789,110881,110993,111117,111184,111310,111378,111506,111650,111778,111847,111942,112057,112170,112269,112378,112489,112600,112701,112806,112906,113036,113127,113250,113344,113456,113542,113646,113742,113830,113948,114052,114156,114282,114370,114478,114578,114668,114778,114862,114964,115048,115102,115166,115272,115358,115468,115552,115672,120816,120934,121049,121181,121896,122588,123105,124704,126237,126625,131360,151622,151882,153392,154425,156438,156700,157056,157886,164668,165802,166096,166319,166646,168696,169344,173195,174397,178476,179691,181100,181574"}, "to": {"startLines": "36,59,60,91,92,93,94,97,98,99,100,101,102,103,106,107,108,109,112,113,114,115,116,117,122,123,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,259,260,265,266,267,268,269,270,271,299,300,301,302,303,304,305,306,342,343,344,345,350,358,359,364,386,392,393,394,395,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,464,469,470,477,478,479,480,488,489,493,497,501,506,512,519,523,527,532,536,540,544,548,552,556,562,566,572,576,582,586,591,595,598,602,608,612,618,622,628,631,635,639,643,647,651,652,653,654,657,660,663,666,670,671,672,673,674,677,679,681,683,688,689,693,699,703,704,706,717,718,722,728,732,733,734,738,765,769,770,774,802,971,997,1166,1192,1223,1231,1237,1251,1273,1278,1283,1293,1302,1311,1315,1322,1330,1337,1338,1347,1350,1353,1357,1361,1365,1368,1369,1374,1379,1389,1394,1401,1407,1408,1411,1415,1420,1422,1424,1427,1430,1432,1436,1439,1446,1449,1452,1456,1458,1462,1464,1466,1468,1472,1480,1488,1500,1506,1515,1518,1529,1532,1533,1538,1539,1568,1637,1707,1708,1718,1727,1879,1881,1885,1888,1891,1894,1897,1900,1903,1906,1910,1913,1916,1919,1923,1926,1930,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1956,1958,1959,1960,1961,1962,1963,1964,1965,1967,1968,1970,1971,1973,1975,1976,1978,1979,1980,1981,1982,1983,1985,1986,1987,1988,1989,2001,2003,2005,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2019,2021,2022,2023,2024,2025,2026,2027,2029,2033,2037,2038,2039,2040,2041,2042,2046,2047,2048,2049,2051,2053,2055,2057,2059,2060,2061,2062,2064,2066,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2082,2083,2084,2085,2087,2089,2090,2092,2093,2095,2097,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2110,2112,2113,2114,2115,2117,2118,2119,2120,2121,2123,2125,2127,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2149,2224,2227,2230,2233,2247,2264,2306,2335,2362,2371,2433,2797,2828,2966,3090,3114,3120,3136,3157,3281,3309,3315,3459,3485,3533,3604,3704,3724,3779,3791,3817", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3187,3383,3447,3517,3578,3653,3729,3806,4044,4129,4211,4287,4464,4541,4619,4725,4831,4910,5239,5296,5485,5559,5634,5699,5765,5825,5886,5958,6075,6142,6210,6269,6328,6387,6446,6505,6559,6613,6666,6720,6774,6828,7083,7157,7236,7309,7383,7454,7526,7598,7671,7728,7786,7859,7933,8007,8082,8154,8227,8297,8368,8428,8489,8558,8627,8697,8771,8847,8911,8988,9064,9141,9206,9275,9352,9427,9496,9564,9641,9707,9768,9865,9930,9999,10098,10169,10228,10286,10343,10402,10466,10537,10609,10681,10753,10825,10892,10960,11028,11087,11150,11214,11304,11395,11455,11521,11588,11654,11724,11788,11841,11908,11969,12036,12149,12207,12270,12335,12400,12475,12548,12620,12669,12730,12791,12852,12914,12978,13042,13106,13171,13234,13294,13355,13421,13480,13540,13602,13673,13733,14432,14518,14821,14911,14998,15086,15168,15251,15341,17160,17212,17270,17315,17381,17445,17502,17559,19736,19793,19841,19890,20145,20515,20562,20820,21991,22294,22358,22420,22480,22801,22875,22945,23023,23077,23147,23232,23280,23326,23387,23450,23516,23580,23651,23714,23779,23843,23904,23965,24017,24090,24164,24233,24308,24382,24456,24597,27655,28016,28094,28484,28572,28668,28758,29340,29429,29676,29957,30209,30494,30887,31364,31586,31808,32084,32311,32541,32771,33001,33231,33458,33877,34103,34528,34758,35186,35405,35688,35896,36027,36254,36680,36905,37332,37553,37978,38098,38374,38675,38999,39290,39604,39741,39872,39977,40219,40386,40590,40798,41069,41181,41293,41398,41515,41729,41875,42015,42101,42449,42537,42783,43201,43450,43532,43630,44222,44322,44574,44998,45253,45347,45436,45673,47697,47939,48041,48294,50450,60891,62407,72946,74474,76231,76857,77277,78338,79603,79859,80095,80642,81136,81741,81939,82519,83083,83458,83576,84114,84271,84467,84740,84996,85166,85307,85371,85736,86103,86779,87043,87381,87734,87828,88014,88320,88582,88707,88834,89073,89284,89403,89596,89773,90228,90409,90531,90790,90903,91090,91192,91299,91428,91703,92211,92707,93584,93878,94448,94597,95329,95501,95585,95921,96013,98079,103325,108714,108776,109354,109938,117885,117998,118227,118387,118539,118710,118876,119045,119212,119375,119618,119788,119961,120132,120406,120605,120810,121140,121224,121320,121416,121514,121614,121716,121818,121920,122022,122124,122224,122320,122432,122561,122684,122815,122946,123044,123158,123252,123392,123526,123622,123734,123834,123950,124046,124158,124258,124398,124534,124698,124828,124986,125136,125277,125421,125556,125668,125818,125946,126074,126210,126342,126472,126602,126714,127612,127758,127902,128040,128106,128196,128272,128376,128466,128568,128676,128784,128884,128964,129056,129154,129264,129316,129394,129500,129592,129696,129806,129928,130091,130248,130328,130428,130518,130628,130718,130959,131053,131159,131251,131351,131463,131577,131693,131809,131903,132017,132129,132231,132351,132473,132555,132659,132779,132905,133003,133097,133185,133297,133413,133535,133647,133822,133938,134024,134116,134228,134352,134419,134545,134613,134741,134885,135013,135082,135177,135292,135405,135504,135613,135724,135835,135936,136041,136141,136271,136362,136485,136579,136691,136777,136881,136977,137065,137183,137287,137391,137517,137605,137713,137813,137903,138013,138097,138199,138283,138337,138401,138507,138593,138703,138787,139191,141807,141925,142040,142120,142481,143067,144471,145815,147176,147564,150339,160428,161468,168281,172582,173333,173595,174127,174506,178784,179638,179867,184475,185485,187020,189420,193544,194288,196419,196759,198070", "endLines": "36,59,60,91,92,93,94,97,98,99,100,101,102,103,106,107,108,109,112,113,114,115,116,117,122,123,126,127,128,129,130,131,132,133,135,136,137,138,139,140,141,142,143,144,145,146,147,148,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,259,260,265,266,267,268,269,270,271,299,300,301,302,303,304,305,306,342,343,344,345,350,358,359,364,386,392,393,394,395,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,464,469,470,477,478,479,487,488,492,496,500,505,511,518,522,526,531,535,539,543,547,551,555,561,565,571,575,581,585,590,594,597,601,607,611,617,621,627,630,634,638,642,646,650,651,652,653,656,659,662,665,669,670,671,672,673,676,678,680,682,687,688,692,698,702,703,705,716,717,721,727,731,732,733,737,764,768,769,773,801,970,996,1165,1191,1222,1230,1236,1250,1272,1277,1282,1292,1301,1310,1314,1321,1329,1336,1337,1346,1349,1352,1356,1360,1364,1367,1368,1373,1378,1388,1393,1400,1406,1407,1410,1414,1419,1421,1423,1426,1429,1431,1435,1438,1445,1448,1451,1455,1457,1461,1463,1465,1467,1471,1479,1487,1499,1505,1514,1517,1528,1531,1532,1537,1538,1543,1636,1706,1707,1717,1726,1727,1880,1884,1887,1890,1893,1896,1899,1902,1905,1909,1912,1915,1918,1922,1925,1929,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1955,1957,1958,1959,1960,1961,1962,1963,1964,1966,1967,1969,1970,1972,1974,1975,1977,1978,1979,1980,1981,1982,1984,1985,1986,1987,1988,1989,2002,2004,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015,2016,2017,2018,2020,2021,2022,2023,2024,2025,2026,2028,2032,2036,2037,2038,2039,2040,2041,2045,2046,2047,2048,2050,2052,2054,2056,2058,2059,2060,2061,2063,2065,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2081,2082,2083,2084,2086,2088,2089,2091,2092,2094,2096,2098,2099,2100,2101,2102,2103,2104,2105,2106,2107,2108,2109,2111,2112,2113,2114,2116,2117,2118,2119,2120,2122,2124,2126,2128,2129,2130,2131,2132,2133,2134,2135,2136,2137,2138,2139,2140,2141,2142,2143,2223,2226,2229,2232,2246,2252,2273,2334,2361,2370,2432,2791,2800,2855,2983,3113,3119,3125,3156,3280,3300,3314,3318,3464,3519,3544,3669,3723,3778,3790,3816,3823", "endColumns": "54,44,48,40,54,58,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3182,3244,3442,3512,3573,3648,3724,3801,3879,4124,4206,4282,4358,4536,4614,4720,4826,4905,4985,5291,5349,5554,5629,5694,5760,5820,5881,5953,6026,6137,6205,6264,6323,6382,6441,6500,6554,6608,6661,6715,6769,6823,6877,7152,7231,7304,7378,7449,7521,7593,7666,7723,7781,7854,7928,8002,8077,8149,8222,8292,8363,8423,8484,8553,8622,8692,8766,8842,8906,8983,9059,9136,9201,9270,9347,9422,9491,9559,9636,9702,9763,9860,9925,9994,10093,10164,10223,10281,10338,10397,10461,10532,10604,10676,10748,10820,10887,10955,11023,11082,11145,11209,11299,11390,11450,11516,11583,11649,11719,11783,11836,11903,11964,12031,12144,12202,12265,12330,12395,12470,12543,12615,12664,12725,12786,12847,12909,12973,13037,13101,13166,13229,13289,13350,13416,13475,13535,13597,13668,13728,13796,14513,14600,14906,14993,15081,15163,15246,15336,15427,17207,17265,17310,17376,17440,17497,17554,17608,19788,19836,19885,19936,20174,20557,20606,20861,22018,22353,22415,22475,22532,22870,22940,23018,23072,23142,23227,23275,23321,23382,23445,23511,23575,23646,23709,23774,23838,23899,23960,24012,24085,24159,24228,24303,24377,24451,24592,24662,27703,28089,28179,28567,28663,28753,29335,29424,29671,29952,30204,30489,30882,31359,31581,31803,32079,32306,32536,32766,32996,33226,33453,33872,34098,34523,34753,35181,35400,35683,35891,36022,36249,36675,36900,37327,37548,37973,38093,38369,38670,38994,39285,39599,39736,39867,39972,40214,40381,40585,40793,41064,41176,41288,41393,41510,41724,41870,42010,42096,42444,42532,42778,43196,43445,43527,43625,44217,44317,44569,44993,45248,45342,45431,45668,47692,47934,48036,48289,50445,60886,62402,72941,74469,76226,76852,77272,78333,79598,79854,80090,80637,81131,81736,81934,82514,83078,83453,83571,84109,84266,84462,84735,84991,85161,85302,85366,85731,86098,86774,87038,87376,87729,87823,88009,88315,88577,88702,88829,89068,89279,89398,89591,89768,90223,90404,90526,90785,90898,91085,91187,91294,91423,91698,92206,92702,93579,93873,94443,94592,95324,95496,95580,95916,96008,96286,103320,108709,108771,109349,109933,110024,117993,118222,118382,118534,118705,118871,119040,119207,119370,119613,119783,119956,120127,120401,120600,120805,121135,121219,121315,121411,121509,121609,121711,121813,121915,122017,122119,122219,122315,122427,122556,122679,122810,122941,123039,123153,123247,123387,123521,123617,123729,123829,123945,124041,124153,124253,124393,124529,124693,124823,124981,125131,125272,125416,125551,125663,125813,125941,126069,126205,126337,126467,126597,126709,126849,127753,127897,128035,128101,128191,128267,128371,128461,128563,128671,128779,128879,128959,129051,129149,129259,129311,129389,129495,129587,129691,129801,129923,130086,130243,130323,130423,130513,130623,130713,130954,131048,131154,131246,131346,131458,131572,131688,131804,131898,132012,132124,132226,132346,132468,132550,132654,132774,132900,132998,133092,133180,133292,133408,133530,133642,133817,133933,134019,134111,134223,134347,134414,134540,134608,134736,134880,135008,135077,135172,135287,135400,135499,135608,135719,135830,135931,136036,136136,136266,136357,136480,136574,136686,136772,136876,136972,137060,137178,137282,137386,137512,137600,137708,137808,137898,138008,138092,138194,138278,138332,138396,138502,138588,138698,138782,138902,141802,141920,142035,142115,142476,142709,143579,145810,147171,147559,150334,160238,160558,162820,168848,173328,173590,173790,174501,178779,179385,179862,180013,184685,186563,187327,192441,194283,196414,196754,198065,198268"}}, {"source": "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\firebase_crashlytics-3.5.7\\android\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "60", "endOffsets": "111"}, "to": {"startLines": "95", "startColumns": "4", "startOffsets": "3249", "endColumns": "60", "endOffsets": "3305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "261,262,263,273,274,275,351,3465", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "14605,14664,14712,15478,15553,15629,20179,184690", "endLines": "261,262,263,273,274,275,351,3484", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14659,14707,14763,15548,15624,15696,20240,185480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2146,2856,2862", "startColumns": "4,4,4,4", "startOffsets": "1213,139046,162825,163036", "endLines": "35,2148,2861,2945", "endColumns": "60,12,24,24", "endOffsets": "1269,139186,163031,167547"}}, {"source": "C:\\Users\\<USER>\\AMSSoftX\\project\\Andriod Projects\\Andriod Apps Workspace\\updated\\wargani\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1561,1565", "startColumns": "4,4", "startOffsets": "97729,97910", "endLines": "1564,1567", "endColumns": "12,12", "endOffsets": "97905,98074"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "387", "startColumns": "4", "startOffsets": "22023", "endColumns": "42", "endOffsets": "22061"}}, {"source": "C:\\Users\\<USER>\\AMSSoftX\\project\\Andriod Projects\\Andriod Apps Workspace\\updated\\wargani\\android\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,569", "endColumns": "81,103,108,119,98,67", "endOffsets": "132,236,345,465,564,632"}, "to": {"startLines": "451,455,456,457,458,463", "startColumns": "4,4,4,4,4,4", "startOffsets": "26593,26971,27075,27184,27304,27587", "endColumns": "81,103,108,119,98,67", "endOffsets": "26670,27070,27179,27299,27398,27650"}}]}]}