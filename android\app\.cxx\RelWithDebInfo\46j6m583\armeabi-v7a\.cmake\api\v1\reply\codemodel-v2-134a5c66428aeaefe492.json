{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/AMSSoftX/project/Andriod Projects/Andriod Apps Workspace/updated/wargani/android/app/.cxx/RelWithDebInfo/46j6m583/armeabi-v7a", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}